# Transfer Status Feature

## Overview
Added a new `is_transfer` field to the `transaction_summary_report_detail` table to track whether transfers have been processed for each merchant record in transaction summary reports.

## Database Changes

### New Field Added
```sql
ALTER TABLE transaction_summary_report_detail
ADD COLUMN is_transfer SMALLINT DEFAULT 0 CHECK (is_transfer IN (0, 1));
```

### Field Details
- **Type**: `SMALLINT` with constraint to allow only 0 or 1
- **Default**: `0` (not yet transferred)
- **Values**:
  - `0` = Transfer not yet processed
  - `1` = Transfer already processed
- **Indexed**: Yes, for query performance
- **NOT NULL**: Yes, with default value

## Backend Implementation

### Updated Service
**File**: `src/main/services/transactionSummaryReportService.ts`

- ✅ Added `isTransfer` field to `MerchantSummaryData` interface
- ✅ Default value set to `0` for new records
- ✅ Database insert statement updated to include new field
- ✅ Parameter array updated with new field value

### New IPC Handlers
**File**: `src/main/handler/transactionHandler.ts`

#### 1. `update-transfer-status`
Updates transfer status for a single record:
```typescript
// Parameters: detailId, isTransfer (0|1), updatedBy
await safeIpcInvoke('update-transfer-status', detailId, 1, 'username');
```

#### 2. `bulk-update-transfer-status`
Updates transfer status for multiple records:
```typescript
// Parameters: { detailIds: number[], isTransfer: 0|1, updatedBy: string }
await safeIpcInvoke('bulk-update-transfer-status', {
  detailIds: [1, 2, 3],
  isTransfer: 1,
  updatedBy: 'username'
});
```

#### 3. `get-transfer-status-report`
Retrieves transfer status report with filtering:
```typescript
// Parameters: { startDate?, endDate?, isTransfer?, page?, pageSize? }
await safeIpcInvoke('get-transfer-status-report', {
  startDate: '2025-07-01',
  endDate: '2025-07-31',
  isTransfer: 0, // Only pending transfers
  page: 1,
  pageSize: 50
});
```

## Frontend Implementation

### Updated Transaction Summary Screen
**File**: `src/renderer/screens/transaction-summary.screen.tsx`

#### New Features:
1. **Transfer Status Column**: Added to summary table showing visual status indicators
2. **Status Badges**:
   - 🟢 `✅ Transferred` (green) for completed transfers
   - 🟡 `⏳ Pending` (yellow) for pending transfers
3. **Data Integration**: Loads transfer status from database records

#### Visual Indicators:
```tsx
<span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
  item.isTransfer === 1
    ? 'bg-green-100 text-green-800'
    : 'bg-yellow-100 text-yellow-800'
}`}>
  {item.isTransfer === 1 ? '✅ Transferred' : '⏳ Pending'}
</span>
```

## Migration Files

### 1. Main Migration
**File**: `database_migration_create_transaction_summary_report.sql`
- Updated to include `is_transfer` field in new installations

### 2. Upgrade Migration
**File**: `database_migration_add_transfer_fee_status.sql`
- Adds field to existing installations
- Sets default values for existing records
- Adds constraints and indexes
- Includes verification queries

## Usage Examples

### Database Queries

#### Check Transfer Status Summary
```sql
SELECT 
    is_transfer_fee,
    CASE 
        WHEN is_transfer_fee = 0 THEN 'Pending Transfer'
        WHEN is_transfer_fee = 1 THEN 'Transfer Completed'
    END as status_description,
    COUNT(*) as record_count,
    SUM(final_net_amount) as total_amount
FROM transaction_summary_report_detail
GROUP BY is_transfer_fee;
```

#### Find Pending Transfers
```sql
SELECT d.*, r.report_date, r.running_number 
FROM transaction_summary_report_detail d
JOIN transaction_summary_report r ON d.report_id = r.id
WHERE d.is_transfer_fee = 0
ORDER BY d.transaction_date DESC;
```

#### Mark Transfers as Complete
```sql
UPDATE transaction_summary_report_detail 
SET is_transfer_fee = 1, 
    update_by = 'username', 
    update_dt = CURRENT_TIMESTAMP
WHERE merchant_vat = '1234567890' 
AND transaction_date = '2025-07-19';
```

### Frontend Usage

#### Load Transfer Status Report
```typescript
const result = await safeIpcInvoke('get-transfer-fee-status-report', {
  startDate: '2025-07-01',
  endDate: '2025-07-31',
  isTransferFee: 0, // Only pending
  page: 1,
  pageSize: 50
});
```

#### Update Single Record
```typescript
const result = await safeIpcInvoke('update-transfer-fee-status', 
  detailId, 1, user.user_name);
```

#### Bulk Update Multiple Records
```typescript
const result = await safeIpcInvoke('bulk-update-transfer-fee-status', {
  detailIds: selectedIds,
  isTransferFee: 1,
  updatedBy: user.user_name
});
```

## Benefits

### 1. Transfer Tracking
- ✅ Track which merchant transfers have been processed
- ✅ Identify pending transfers requiring action
- ✅ Audit trail of transfer status changes

### 2. Operational Efficiency
- ✅ Visual indicators in UI for quick status identification
- ✅ Bulk update capabilities for processing multiple transfers
- ✅ Filtered reporting for focused workflow management

### 3. Data Integrity
- ✅ Database constraints ensure valid status values
- ✅ Audit columns track who made changes and when
- ✅ Default values prevent null status issues

### 4. Reporting Capabilities
- ✅ Transfer status reports with pagination
- ✅ Date range filtering for specific periods
- ✅ Summary statistics for transfer completion rates

## Deployment Steps

### For New Installations
1. Use the updated `database_migration_create_transaction_summary_report.sql`
2. The field will be included automatically

### For Existing Installations
1. Run the upgrade migration:
   ```bash
   psql "connection_string" -f database_migration_add_transfer_fee_status.sql
   ```
2. Restart the application to load new handlers
3. Verify the field was added correctly

## Future Enhancements

### Potential Additions
1. **Transfer Date Tracking**: Add `transfer_date` field to record when transfer was completed
2. **Transfer Reference**: Add `transfer_reference` field for external transfer IDs
3. **Batch Transfer Processing**: Group transfers by date/batch for bulk processing
4. **Transfer Notifications**: Email/SMS notifications for pending transfers
5. **Transfer Reports**: Dedicated transfer management screen with advanced filtering

The transfer fee status feature provides comprehensive tracking and management capabilities for transfer fee processing, enhancing operational efficiency and providing clear audit trails for financial operations.
