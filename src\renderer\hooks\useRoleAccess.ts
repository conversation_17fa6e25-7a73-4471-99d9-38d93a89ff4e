
import { useAuth } from '../contexts/AuthContext';

/**
 * Role-based access control hook for Master Data operations
 */
export function useRoleAccess() {
  const { user } = useAuth();
  
  const userRole = user?.role_code?.toLowerCase();
  
  // Define role permissions
  const permissions = {
    // Admin and Maker roles have full access
    canCreate: userRole === 'admin' || userRole === 'maker',
    canUpdate: userRole === 'admin' || userRole === 'maker', 
    canDelete: userRole === 'admin' || userRole === 'maker',
    
    // All authenticated users can view, search, filter
    canView: !!user,
    canSearch: !!user,
    canFilter: !!user,
    canExport: !!user,
    
    // Role-specific checks
    isAdmin: userRole === 'admin',
    isMaker: userRole === 'maker',
    isViewer: userRole === 'view',
    
    // Combined permissions
    hasWriteAccess: userRole === 'admin' || userRole === 'maker',
    hasReadOnlyAccess: userRole === 'view',
  };
  
  return {
    ...permissions,
    userRole,
    user,
  };
}

/**
 * Hook specifically for Master Data section permissions
 */
export function useMasterDataAccess() {
  const roleAccess = useRoleAccess();
  
  return {
    ...roleAccess,
    // Master Data specific permissions
    canManageBanks: roleAccess.hasWriteAccess,
    canManageMerchants: roleAccess.hasWriteAccess,
    canManageNetworkServices: roleAccess.hasWriteAccess,
    canManageCompanySettings: roleAccess.isAdmin, // Only admin can manage company settings
    canManageUsers: roleAccess.isAdmin, // Only admin can manage users
  };
}

/**
 * Hook for checking specific permissions
 */
export function usePermission(permission: keyof ReturnType<typeof useRoleAccess>): boolean {
  const roleAccess = useRoleAccess();
  return Boolean(roleAccess[permission]);
}
