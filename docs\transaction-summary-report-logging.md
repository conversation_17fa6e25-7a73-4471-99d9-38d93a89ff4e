# Automatic Transaction Summary Report Logging

## Overview
This feature implements automatic logging of Transaction Summary Reports into database tables whenever transaction files are uploaded through the Transaction Management upload screen. It provides a persistent audit trail and enables historical tracking of all transaction summaries.

## Database Schema

### Main Tables Created

#### 1. `transaction_summary_report`
Main table storing report metadata and grand totals:

```sql
CREATE TABLE transaction_summary_report (
    id BIGSERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    report_time TIME NOT NULL,
    running_number VARCHAR(50),
    batch_id VARCHAR(100) UNIQUE NOT NULL,
    processed_files TEXT[],
    total_files INTEGER DEFAULT 0,
    total_transactions INTEGER DEFAULT 0,
    grand_total_amount DECIMAL(18,2) DEFAULT 0,
    grand_total_mdr_amount DECIMAL(18,2) DEFAULT 0,
    grand_total_vat_amount DECIMAL(18,2) DEFAULT 0,
    grand_total_net_amount DECIMAL(18,2) DEFAULT 0,
    grand_total_withhold_tax DECIMAL(18,2) DEFAULT 0,
    grand_total_transfer_fee DECIMAL(18,2) DEFAULT 0,
    grand_total_reimbursement_fee DECIMAL(18,2) DEFAULT 0,
    grand_total_service_fee DECIMAL(18,2) DEFAULT 0,
    grand_total_final_net_amount DECIMAL(18,2) DEFAULT 0,
    grand_total_cup_business_tax_fee DECIMAL(18,2) DEFAULT 0,
    average_mdr_rate DECIMAL(8,4) DEFAULT 0,
    average_vat_percentage DECIMAL(8,4) DEFAULT 0,
    average_withholding_tax_rate DECIMAL(8,4) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'GENERATED',
    approved_by VARCHAR(100),
    approved_at TIMESTAMP,
    create_by VARCHAR(100) DEFAULT 'SYSTEM',
    create_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(100) DEFAULT 'SYSTEM',
    update_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. `transaction_summary_report_detail`
Detail table storing merchant-level data:

```sql
CREATE TABLE transaction_summary_report_detail (
    id BIGSERIAL PRIMARY KEY,
    report_id BIGINT NOT NULL REFERENCES transaction_summary_report(id),
    merchant_vat VARCHAR(100),
    merchant_name VARCHAR(255),
    transaction_date DATE,
    channel_type VARCHAR(50),
    transaction_count INTEGER DEFAULT 0,
    total_amount DECIMAL(18,2) DEFAULT 0,
    mdr_rate DECIMAL(8,4) DEFAULT 0,
    mdr_amount DECIMAL(18,2) DEFAULT 0,
    vat_percentage DECIMAL(8,4) DEFAULT 0,
    vat_amount DECIMAL(18,2) DEFAULT 0,
    net_amount DECIMAL(18,2) DEFAULT 0,
    withholding_tax_rate DECIMAL(8,4) DEFAULT 0,
    withhold_tax DECIMAL(18,2) DEFAULT 0,
    transfer_fee DECIMAL(18,2) DEFAULT 0,
    reimbursement_fee DECIMAL(18,2) DEFAULT 0,
    service_fee DECIMAL(18,2) DEFAULT 0,
    final_net_amount DECIMAL(18,2) DEFAULT 0,
    cup_business_tax_fee DECIMAL(18,2) DEFAULT 0,
    is_transfer SMALLINT DEFAULT 0 CHECK (is_transfer IN (0, 1)),
    create_by VARCHAR(100) DEFAULT 'SYSTEM',
    create_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(100) DEFAULT 'SYSTEM',
    update_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Implementation Components

### 1. TransactionSummaryReportService
**File**: `src/main/services/transactionSummaryReportService.ts`

Main service class that handles:
- Generating report data using the same calculation logic as PDF reports
- Saving report data to database tables
- Financial calculations (MDR, VAT, withholding tax, fees)
- Merchant grouping and aggregation

**Key Methods**:
- `generateAndSaveReport()`: Main entry point for report generation
- `generateReportData()`: Creates report data structure
- `saveReportToDatabase()`: Persists data to database tables

### 2. Integration with Transaction Processing
**File**: `src/main/handler/transactionHandler.ts`

The report generation is automatically triggered in the `process-transaction-files` IPC handler:

```typescript
// After successful file processing
if (results.processedFiles > 0 && results.savedTransactions > 0) {
  const batchId = `BATCH_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const processedFileNames = filesToProcess
    .filter(file => file.processed)
    .map(file => file.fileName);

  const reportResult = await summaryReportService.generateAndSaveReport(
    batchId,
    processedFileNames,
    results.processedFiles,
    results.savedTransactions,
    currentUser
  );
}
```

### 3. New IPC Handlers
Added three new IPC handlers for report management:

#### `get-transaction-summary-reports`
Retrieves paginated list of summary reports with filtering:
- Date range filtering
- Status filtering (GENERATED, APPROVED, CANCELLED)
- Pagination support

#### `get-transaction-summary-report-details`
Fetches detailed data for a specific report including:
- Main report metadata
- Merchant-level detail records

#### `approve-transaction-summary-report`
Approves a report and updates status:
- Sets status to 'APPROVED'
- Records approver and approval timestamp

#### `update-transfer-status`
Updates transfer status for individual records:
- Sets is_transfer to 0 (not transferred) or 1 (transferred)
- Records who made the update and when

#### `bulk-update-transfer-status`
Updates transfer status for multiple records:
- Batch update for efficiency
- Same validation as individual update

#### `get-transfer-status-report`
Retrieves transfer status report with filtering:
- Filter by date range and transfer status
- Pagination support
- Includes merchant and report details

### 4. Updated Transaction Summary Screen
**File**: `src/renderer/screens/transaction-summary.screen.tsx`

Enhanced to use real database data:
- Loads actual report data instead of mock data
- Implements real approval functionality
- Maintains fallback to mock data for demonstration

## Workflow Integration

### Automatic Report Generation Process

1. **File Upload**: User uploads transaction files via Transaction Management screen
2. **File Processing**: Files are processed and transactions saved to database
3. **Report Generation**: Automatically triggered after successful processing
4. **Data Calculation**: Uses same logic as PDF report generation:
   - Groups transactions by merchant VAT
   - Calculates MDR, VAT, withholding tax, fees
   - Computes grand totals and averages
5. **Database Storage**: Saves report and detail records
6. **Audit Trail**: Records batch ID, processed files, user, timestamp

### Financial Calculations
The system uses the same calculation methodology as the existing PDF reports:

- **MDR Amount** = Transaction Amount × (MDR% ÷ 100) [from merchant_wechat.wechat_rate]
- **VAT Amount** = MDR Amount × (VAT% ÷ 100) [from network_service.vat_tax_percent]
- **Net Amount** = Transaction Amount - MDR Amount
- **Withholding Tax** = MDR Amount × (Withholding Tax% ÷ 100) [from merchant.withholding_tax]
- **Reimbursement Fee** = Transaction Amount × 0.5%
- **Final Net Amount** = Total Amount - (Transfer Fee + Reimbursement Fee + Service Fee + CUP Business Tax)

## Benefits

### 1. Audit Trail
- Complete history of all transaction summaries
- Tracks who processed files and when
- Links reports to specific file processing batches

### 2. Data Consistency
- Uses identical calculation logic as PDF reports
- Ensures data integrity between different views
- Maintains referential integrity with foreign keys

### 3. Performance
- Indexed tables for fast querying
- Paginated data retrieval
- Efficient merchant grouping

### 4. Approval Workflow
- Status tracking (GENERATED → APPROVED)
- Approval audit trail with user and timestamp
- Integration with role-based permissions

## Usage Examples

### Query Recent Reports
```sql
SELECT * FROM transaction_summary_report 
WHERE report_date >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY create_dt DESC;
```

### Get Merchant Details for a Report
```sql
SELECT d.* FROM transaction_summary_report_detail d
JOIN transaction_summary_report r ON d.report_id = r.id
WHERE r.batch_id = 'BATCH_1234567890_abc123def';
```

### Find Approved Reports
```sql
SELECT * FROM transaction_summary_report
WHERE status = 'APPROVED'
AND approved_at >= '2025-07-01';
```

### Query Transfer Fee Status
```sql
-- Get all pending transfers
SELECT d.*, r.report_date, r.running_number
FROM transaction_summary_report_detail d
JOIN transaction_summary_report r ON d.report_id = r.id
WHERE d.is_transfer = 0
ORDER BY d.transaction_date DESC;

-- Get completed transfers for a specific date
SELECT d.*, r.report_date, r.running_number
FROM transaction_summary_report_detail d
JOIN transaction_summary_report r ON d.report_id = r.id
WHERE d.is_transfer = 1
AND d.transaction_date = '2025-07-19';

-- Summary of transfer status
SELECT
    is_transfer,
    CASE
        WHEN is_transfer = 0 THEN 'Pending Transfer'
        WHEN is_transfer = 1 THEN 'Transfer Completed'
    END as status_description,
    COUNT(*) as record_count,
    SUM(final_net_amount) as total_amount
FROM transaction_summary_report_detail
GROUP BY is_transfer;
```

## Migration Instructions

1. **Run Database Migration**:
   ```bash
   psql "your_connection_string" -f database_migration_create_transaction_summary_report.sql
   ```

2. **Restart Application**: The new services and handlers will be automatically loaded

3. **Test Workflow**:
   - Upload transaction files via Transaction Management
   - Verify reports are automatically generated
   - Check Transaction Summary screen for real data

## Future Enhancements

1. **Report Scheduling**: Automatic daily/weekly report generation
2. **Email Notifications**: Send reports to stakeholders
3. **Data Export**: Export historical reports to Excel/PDF
4. **Advanced Analytics**: Trend analysis and reporting
5. **Report Templates**: Customizable report formats
