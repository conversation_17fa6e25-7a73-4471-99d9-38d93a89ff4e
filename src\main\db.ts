import { Client } from 'pg';
import { getDatabaseConfig } from './config/env';

export interface PostgreSQLConnectionConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean;
}

// Get database configuration from environment
const getDefaultDbConfig = (): PostgreSQLConnectionConfig => {
  const config = getDatabaseConfig();
  return {
    host: config.host,
    port: config.port,
    database: config.database,
    user: config.user,
    password: config.password,
    ssl: config.ssl,
  };
};

/**
 * Get a database connection with optional custom configuration
 * @param config Optional custom database configuration
 * @returns Promise<Client> PostgreSQL client instance
 */
export const getDbConnection = async (config?: PostgreSQLConnectionConfig): Promise<Client> => {
  const dbConfig = config || getDefaultDbConfig();
  const envConfig = getDatabaseConfig();

  const client = new Client({
    host: dbConfig.host,
    port: dbConfig.port,
    database: dbConfig.database,
    user: dbConfig.user,
    password: dbConfig.password,
    ssl: dbConfig.ssl ? { rejectUnauthorized: false } : false,
    connectionTimeoutMillis: envConfig.connectionTimeoutMillis,
    query_timeout: envConfig.query_timeout,
  });

  await client.connect();
  return client;
};

/**
 * Execute a database query with automatic connection management
 * @param queryText SQL query string
 * @param params Query parameters
 * @param config Optional custom database configuration
 * @returns Promise with query result
 */
export const executeQuery = async (
  queryText: string,
  params: any[] = [],
  config?: PostgreSQLConnectionConfig
) => {
  let client: Client | null = null;

  try {
    client = await getDbConnection(config);
    const result = await client.query(queryText, params);
    return result;
  } finally {
    if (client) {
      await client.end();
    }
  }
};

/**
 * Execute multiple queries in a transaction
 * @param queries Array of query objects with text and params
 * @param config Optional custom database configuration
 * @returns Promise with transaction result
 */
export const executeTransaction = async (
  queries: Array<{ text: string; params?: any[] }>,
  config?: PostgreSQLConnectionConfig
) => {
  let client: Client | null = null;

  try {
    client = await getDbConnection(config);
    await client.query('BEGIN');

    const results = [];
    for (const query of queries) {
      const result = await client.query(query.text, query.params || []);
      results.push(result);
    }

    await client.query('COMMIT');
    return results;
  } catch (error) {
    if (client) {
      await client.query('ROLLBACK');
    }
    throw error;
  } finally {
    if (client) {
      await client.end();
    }
  }
};

/**
 * Test database connection
 * @param config Optional custom database configuration
 * @returns Promise<boolean> Connection success status
 */
export const testConnection = async (config?: PostgreSQLConnectionConfig): Promise<boolean> => {
  let client: Client | null = null;

  try {
    client = await getDbConnection(config);
    await client.query('SELECT 1');
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  } finally {
    if (client) {
      await client.end();
    }
  }
};
