import { ipc<PERSON>ain } from 'electron';
import os from 'os';

export function setupSystemHandlers() {
  ipcMain.handle('get-ip-address', async () => {
    try {
      const interfaces = os.networkInterfaces();
      let ipAddress = '127.0.0.1'; // Default to localhost

      for (const name of Object.keys(interfaces)) {
        for (const iface of interfaces[name]!) {
          // Skip over internal (i.e. 127.0.0.1) and non-IPv4 addresses
          if (iface.family === 'IPv4' && !iface.internal) {
            ipAddress = iface.address;
            return { ip_address: ipAddress };
          }
        }
      }
      return { ip_address: ipAddress };
    } catch (error) {
      console.error('Error getting IP address:', error);
      return { ip_address: '127.0.0.1', error: (error as Error).message };
    }
  });
}