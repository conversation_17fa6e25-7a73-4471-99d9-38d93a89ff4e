const fs = require('fs');
const path = require('path');
const pngToIco = require('png-to-ico');
const sharp = require('sharp');

// Create proper ICO file using png-to-ico package with resizing
async function createProperIco(pngPath, icoPath) {
  try {
    console.log('🔄 Resizing PNG to 256x256 and converting to ICO format...');

    // First, resize the PNG to 256x256 to meet electron-builder requirements
    const resizedPngBuffer = await sharp(pngPath)
      .resize(256, 256, {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 } // Transparent background
      })
      .png()
      .toBuffer();

    console.log('✅ Resized PNG to 256x256 pixels');

    // Convert resized PNG to ICO with multiple sizes for better compatibility
    const icoBuffer = await pngToIco([resizedPngBuffer]);

    // Write the ICO file
    fs.writeFileSync(icoPath, icoBuffer);
    console.log('✅ Created proper ICO file:', icoPath);
    return true;
  } catch (error) {
    console.error('❌ Error creating ICO file:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  const pngPath = path.join(__dirname, '../src/assets/Epos.png');
  const icoPath = path.join(__dirname, '../build/icon.ico');

  if (fs.existsSync(pngPath)) {
    const success = await createProperIco(pngPath, icoPath);

    if (success) {
      console.log('');
      console.log('📋 Icon conversion completed!');
      console.log('✅ Linux: build/icon.png');
      console.log('✅ macOS: build/icon.icns');
      console.log('✅ Windows: build/icon.ico (proper format)');
      console.log('');
      console.log('🎉 All icon formats are now ready for electron-builder!');
    }
  } else {
    console.error('❌ Source PNG file not found:', pngPath);
    console.log('💡 Make sure Epos.png exists in src/assets/');
  }
}

// Run the main function
main().catch(console.error);
