import React, { useState, useEffect, useCallback } from "react";
import { Button } from "../button";
import type { Merchant, MerchantWechat } from "../../types/merchant";

interface MerchantWechatTabProps {
  merchantWechat: MerchantWechat | null;
  onSave: (wechatData: Omit<MerchantWechat, "merchant_wechat_id" | "create_dt" | "update_dt">) => Promise<void>;
  editingMerchant: Merchant | null;
  readOnly?: boolean;
}

export function MerchantWechatTab({
  merchantWechat,
  onSave,
  editingMerchant,
  readOnly = false
}: MerchantWechatTabProps) {
  const [formData, setFormData] = useState({
    wechat_rate: '', 
    active: true,
  });
  const [isSaving, setIsSaving] = useState(false);

  // Memoized input change handler to prevent focus loss
  const handleInputChange = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  // Update form data when merchantWechat changes
  useEffect(() => {
    if (merchantWechat) {
      setFormData({
        wechat_rate: (merchantWechat.wechat_rate || '').toString(),
        active: merchantWechat.active,
      });
    } else {
      setFormData({
        wechat_rate: '',
        active: true,
      });
    }
  }, [merchantWechat]);

  const handleSave = async () => {
    if (!editingMerchant?.merchant_id) {
      alert("Please save merchant details first");
      return;
    }

    setIsSaving(true);
    try {
      await onSave({
        merchant_id: editingMerchant.merchant_id,
        wechat_rate: formData.wechat_rate ? parseFloat(formData.wechat_rate) : undefined,
        active: formData.active,
        create_by: "SYSTEM",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Format percentage values
  const formatPercentage = (value?: number) => {
    if (value === undefined || value === null) return "-";
    return `${value}%`;
  };

  // Format date values
  const formatDate = (date?: string | Date) => {
    if (!date) return "-";
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // If in read-only mode, show a comprehensive view
  if (readOnly) {
    return (
      <div className="space-y-6">
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-100">
            WeChat Pay Configuration
          </h4>

          {!merchantWechat ? (
            <div className="text-center py-8">
              <div className="text-gray-500 mb-4">
                <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p className="text-lg font-medium">No WeChat Configuration</p>
                <p className="text-sm">This merchant has no WeChat Pay settings configured.</p>
              </div>
            </div>
          ) : (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex justify-between items-start mb-6">
                <h5 className="text-md font-medium text-gray-900">WeChat Pay Settings</h5>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  merchantWechat.active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                }`}>
                  {merchantWechat.active ? "Active" : "Inactive"}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">WeChat Pay Rate</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formatPercentage(merchantWechat.wechat_rate)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      merchantWechat.active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                    }`}>
                      {merchantWechat.active ? "Enabled" : "Disabled"}
                    </span>
                  </div>
                </div>
              </div>

              {(merchantWechat.create_dt || merchantWechat.update_dt) && (
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500">
                    {merchantWechat.create_dt && (
                      <div>
                        <span className="font-medium">Created:</span> {formatDate(merchantWechat.create_dt)}
                      </div>
                    )}
                    {merchantWechat.update_dt && (
                      <div>
                        <span className="font-medium">Updated:</span> {formatDate(merchantWechat.update_dt)}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  if (!editingMerchant?.merchant_id) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500 mb-4">
          <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-lg font-medium">Save Merchant Details First</p>
          <p className="text-sm">Please save the merchant details before configuring WeChat Pay settings.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">WeChat Pay Settings</h3>
        <p className="text-sm text-gray-600 mb-6">
          Configure WeChat Pay payment processing settings for this merchant.
        </p>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="space-y-6">
          {/* WeChat Rate */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              WeChat Pay Rate (%)
            </label>
            <div className="relative">
              <input
                type="number"
                step="0.01"
                min="0"
                max="100"
                value={formData.wechat_rate}
                onChange={(e) => handleInputChange("wechat_rate", e.target.value ? parseFloat(e.target.value) : '')}
                className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12 ${
                  readOnly ? "bg-gray-50 cursor-not-allowed" : ""
                }`}
                placeholder="0.00"
                readOnly={readOnly}
                disabled={readOnly}
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <span className="text-gray-500 text-sm">%</span>
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              The percentage rate charged for WeChat Pay transactions
            </p>
          </div>

          {/* Active Status */}
          <div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="wechat-active"
                checked={formData.active}
                onChange={(e) => handleInputChange("active", e.target.checked)}
                className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                  readOnly ? "cursor-not-allowed" : ""
                }`}
                disabled={readOnly}
              />
              <label htmlFor="wechat-active" className="ml-2 block text-sm text-gray-900">
                Enable WeChat Pay for this merchant
              </label>
            </div>
            <p className="text-xs text-gray-500 mt-1 ml-6">
              When enabled, this merchant can accept WeChat Pay payments
            </p>
          </div>

          {/* Current Settings Display */}
          {merchantWechat && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Current Settings</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">WeChat Rate:</span>
                  <span className="font-medium">{merchantWechat.wechat_rate || 0}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className={`font-medium ${merchantWechat.active ? 'text-green-600' : 'text-red-600'}`}>
                    {merchantWechat.active ? 'Active' : 'Inactive'}
                  </span>
                </div>
                {merchantWechat.create_dt && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Created:</span>
                    <span className="font-medium">
                      {new Date(merchantWechat.create_dt).toLocaleDateString()}
                    </span>
                  </div>
                )}
                {merchantWechat.update_dt && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Last Updated:</span>
                    <span className="font-medium">
                      {new Date(merchantWechat.update_dt).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Save Button - Hidden in read-only mode */}
          {!readOnly && (
            <div className="flex justify-end pt-4 border-t border-gray-200">
              <Button
                onClick={handleSave}
                variant="primary"
                disabled={isSaving}
                className="min-w-[120px]"
              >
                {isSaving ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </div>
                ) : (
                  merchantWechat ? "Update Settings" : "Save Settings"
                )}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Information Panel */}
      {/* <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              WeChat Pay Information
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>WeChat Pay rate is the percentage fee charged for each transaction</li>
                <li>Rate should be between 0% and 100%</li>
                <li>Disabling WeChat Pay will prevent this merchant from accepting WeChat payments</li>
                <li>Changes take effect immediately after saving</li>
              </ul>
            </div>
          </div>
        </div>
      </div> */}
    </div>
  );
}
