import { ipcMain } from 'electron';
import { Client } from 'pg';
import { getDbConnection } from '../db';

interface MasterDataResponse {
  success: boolean;
  message: string;
  data?: any[];
  error?: string;
}

export function setupMasterDataHandlers() {
  console.log('Setting up Master Data handlers...');
  // Get all groups
  ipcMain.handle('get-groups', async (): Promise<MasterDataResponse> => {
    let client: Client | null = null;

    try {
      console.log('📋 Fetching all groups...');
      client = await getDbConnection();

      const result = await client.query(`
        SELECT group_id, group_name, active,
               create_by, create_dt, update_by, update_dt
        FROM tmst_group
        WHERE active = TRUE
        ORDER BY group_name
      `);

      console.log(`✅ Found ${result.rows.length} groups`);
      return {
        success: true,
        message: `Found ${result.rows.length} groups`,
        data: result.rows
      };

    } catch (error: any) {
      console.error('❌ Error fetching groups:', error.message);
      return {
        success: false,
        message: 'Failed to fetch groups',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get all zones
  ipcMain.handle('get-zones', async (): Promise<MasterDataResponse> => {
    let client: Client | null = null;

    try {
      console.log('📋 Fetching all zones...');
      client = await getDbConnection();

      const result = await client.query(`
        SELECT zone_id, zone_code, zone_name, active,
               create_by, create_dt, update_by, update_dt
        FROM tmst_zone
        WHERE active = TRUE
        ORDER BY zone_name
      `);

      console.log(`✅ Found ${result.rows.length} zones`);
      return {
        success: true,
        message: `Found ${result.rows.length} zones`,
        data: result.rows
      };

    } catch (error: any) {
      console.error('❌ Error fetching zones:', error.message);
      return {
        success: false,
        message: 'Failed to fetch zones',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get all products
  ipcMain.handle('get-products', async (): Promise<MasterDataResponse> => {
    let client: Client | null = null;

    try {
      console.log('📋 Fetching all products...');
      client = await getDbConnection();

      const result = await client.query(`
        SELECT product_id, product_name, active,
               create_by, create_dt, update_by, update_dt
        FROM tmst_product
        WHERE active = TRUE
        ORDER BY product_name
      `);

      console.log(`✅ Found ${result.rows.length} products`);
      return {
        success: true,
        message: `Found ${result.rows.length} products`,
        data: result.rows
      };

    } catch (error: any) {
      console.error('❌ Error fetching products:', error.message);
      return {
        success: false,
        message: 'Failed to fetch products',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get all categories
  ipcMain.handle('get-categories', async (): Promise<MasterDataResponse> => {
    let client: Client | null = null;

    try {
      console.log('📋 Fetching all categories...');
      client = await getDbConnection();

      const result = await client.query(`
        SELECT category_id, category_name, active,
               create_by, create_dt, update_by, update_dt
        FROM tmst_category
        WHERE active = TRUE
        ORDER BY category_name
      `);

      console.log(`✅ Found ${result.rows.length} categories`);
      return {
        success: true,
        message: `Found ${result.rows.length} categories`,
        data: result.rows
      };

    } catch (error: any) {
      console.error('❌ Error fetching categories:', error.message);
      return {
        success: false,
        message: 'Failed to fetch categories',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

}
