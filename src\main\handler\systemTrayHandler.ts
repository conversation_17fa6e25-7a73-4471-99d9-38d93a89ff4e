import { ipc<PERSON><PERSON>, <PERSON>ray, <PERSON>u, BrowserWindow, app, dialog } from 'electron';
import { customIcon } from '../index';

let tray: Tray | null = null;

export function setupSystemTrayHandlers() {
  console.log('Setting up system tray handlers...');

  // Create system tray
  ipcMain.handle('create-system-tray', async () => {
    try {
      if (tray) {
        console.log('📍 System tray already exists');
        return { success: true, message: 'System tray already created' };
      }

      // Create tray with custom icon
      if (customIcon && !customIcon.isEmpty()) {
        tray = new Tray(customIcon);
        console.log('✅ System tray created with custom icon');
      } else {
        console.warn('⚠️ Custom icon not available, system tray not created');
        return { success: false, error: 'Custom icon not available' };
      }

      // Set tray tooltip
      tray.setToolTip('Eposservice Application');

      // Create context menu
      const contextMenu = Menu.buildFromTemplate([
        {
          label: 'Show Application',
          click: () => {
            const mainWindow = BrowserWindow.getAllWindows()[0];
            if (mainWindow) {
              if (mainWindow.isMinimized()) mainWindow.restore();
              mainWindow.show();
              mainWindow.focus();
            }
          }
        },
        {
          label: 'Hide Application',
          click: () => {
            const mainWindow = BrowserWindow.getAllWindows()[0];
            if (mainWindow) {
              mainWindow.hide();
            }
          }
        },
        { type: 'separator' },
        {
          label: 'About',
          click: () => {
            // Show about dialog with custom icon
            const mainWindow = BrowserWindow.getAllWindows()[0];
            const { dialog } = require('electron');
            
            const aboutOptions: any = {
              type: 'info',
              title: 'About Eposservice',
              message: 'Eposservice Application',
              detail: 'Version 1.0.0\nElectron-based POS service management application',
              buttons: ['OK'],
              icon: customIcon || undefined
            };

            if (mainWindow) {
              dialog.showMessageBox(mainWindow, aboutOptions);
            } else {
              dialog.showMessageBox(aboutOptions);
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Quit',
          click: async () => {
            const mainWindow = BrowserWindow.getAllWindows()[0];
            if (mainWindow) {
              // Trigger the same close event as clicking the X button
              mainWindow.close();
            } else {
              app.quit();
            }
          }
        }
      ]);

      // Set context menu
      tray.setContextMenu(contextMenu);

      // Handle tray click events
      tray.on('click', () => {
        const mainWindow = BrowserWindow.getAllWindows()[0];
        if (mainWindow) {
          if (mainWindow.isVisible()) {
            mainWindow.hide();
          } else {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.show();
            mainWindow.focus();
          }
        }
      });

      tray.on('right-click', () => {
        // Context menu will be shown automatically
      });

      tray.on('double-click', () => {
        const mainWindow = BrowserWindow.getAllWindows()[0];
        if (mainWindow) {
          if (mainWindow.isMinimized()) mainWindow.restore();
          mainWindow.show();
          mainWindow.focus();
        }
      });

      return {
        success: true,
        message: 'System tray created successfully'
      };
    } catch (error: any) {
      console.error('❌ Error creating system tray:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });

  // Destroy system tray
  ipcMain.handle('destroy-system-tray', async () => {
    try {
      if (tray) {
        tray.destroy();
        tray = null;
        console.log('🗑️ System tray destroyed');
        return { success: true, message: 'System tray destroyed' };
      } else {
        return { success: true, message: 'System tray was not created' };
      }
    } catch (error: any) {
      console.error('❌ Error destroying system tray:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });

  // Update tray tooltip
  ipcMain.handle('update-tray-tooltip', async (_event, tooltip: string) => {
    try {
      if (tray) {
        tray.setToolTip(tooltip);
        return { success: true, message: 'Tray tooltip updated' };
      } else {
        return { success: false, error: 'System tray not created' };
      }
    } catch (error: any) {
      console.error('❌ Error updating tray tooltip:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });

  // Show tray balloon (Windows only)
  ipcMain.handle('show-tray-balloon', async (_event, options: { title: string; content: string }) => {
    try {
      if (tray && process.platform === 'win32') {
        tray.displayBalloon({
          title: options.title,
          content: options.content,
          icon: customIcon || undefined
        });
        return { success: true, message: 'Tray balloon shown' };
      } else if (!tray) {
        return { success: false, error: 'System tray not created' };
      } else {
        return { success: false, error: 'Tray balloons are only supported on Windows' };
      }
    } catch (error: any) {
      console.error('❌ Error showing tray balloon:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });
}

// Cleanup function to be called when app is quitting
export function cleanupSystemTray() {
  if (tray) {
    tray.destroy();
    tray = null;
    console.log('🧹 System tray cleaned up');
  }
}
