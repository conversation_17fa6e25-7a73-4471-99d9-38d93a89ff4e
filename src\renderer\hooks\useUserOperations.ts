import { useAuth } from '../contexts/AuthContext';
import { createIpcWithUserHook } from '../utils/ipcWithUser';
import { useCurrentUser } from './useCurrentUser';

/**
 * Comprehensive hook for database operations with automatic user field injection
 */
export function useUserOperations() {
  const { user } = useAuth();
  const { 
    currentUserName, 
    withCreateBy, 
    withUpdateBy, 
    withUserFields,
    createFormData,
    updateFormData 
  } = useCurrentUser();
  
  // Create IPC utilities with current user
  const ipc = createIpcWithUserHook(user);

  /**
   * Create a new record with automatic create_by injection
   */
  const createRecord = async <T extends Record<string, any>>(
    channel: string, 
    data: T
  ): Promise<any> => {
    return ipc.create(channel, data);
  };

  /**
   * Update an existing record with automatic update_by injection
   */
  const updateRecord = async <T extends Record<string, any>>(
    channel: string, 
    id: number | string, 
    data: T
  ): Promise<any> => {
    return ipc.update(channel, id, data);
  };

  /**
   * Save a record (create or update) with automatic user field injection
   */
  const saveRecord = async <T extends Record<string, any>>(
    createChannel: string,
    updateChannel: string,
    data: T,
    isUpdate: boolean = false,
    id?: number | string
  ): Promise<any> => {
    return ipc.save(createChannel, updateChannel, data, isUpdate, id);
  };

  /**
   * Create multiple records with automatic create_by injection
   */
  const createRecords = async <T extends Record<string, any>>(
    channel: string, 
    records: T[]
  ): Promise<any> => {
    return ipc.createBatch(channel, records);
  };

  /**
   * Update multiple records with automatic update_by injection
   */
  const updateRecords = async <T extends Record<string, any>>(
    channel: string, 
    records: Array<T & { id: number | string }>
  ): Promise<any> => {
    return ipc.updateBatch(channel, records);
  };

  /**
   * Prepare form data for creating a new record
   */
  const prepareCreateData = <T extends Record<string, any>>(data: T): T & { create_by: string } => {
    return withCreateBy(data);
  };

  /**
   * Prepare form data for updating an existing record
   */
  const prepareUpdateData = <T extends Record<string, any>>(data: T): T & { update_by: string } => {
    return withUpdateBy(data);
  };

  /**
   * Initialize form data for a new record with create_by field
   */
  const initializeCreateForm = <T extends Record<string, any>>(initialData: T): T & { create_by: string } => {
    return createFormData(initialData);
  };

  /**
   * Initialize form data for editing an existing record with update_by field
   */
  const initializeUpdateForm = <T extends Record<string, any>>(existingData: T): T & { update_by: string } => {
    return updateFormData(existingData);
  };

  /**
   * Get current user information
   */
  const getCurrentUser = () => ({
    user_name: currentUserName,
    user_id: user?.user_id,
    role_code: user?.role_code,
    role_name: user?.role_name
  });

  return {
    // User information
    currentUserName,
    getCurrentUser,
    
    // Data preparation utilities
    withCreateBy,
    withUpdateBy,
    withUserFields,
    prepareCreateData,
    prepareUpdateData,
    initializeCreateForm,
    initializeUpdateForm,
    
    // Database operations
    createRecord,
    updateRecord,
    saveRecord,
    createRecords,
    updateRecords,
    
    // Direct IPC access with user injection
    ipc
  };
}

/**
 * Specialized hooks for common operations
 */

/**
 * Hook for merchant operations with automatic user field injection
 */
export function useMerchantOperations() {
  const { createRecord, updateRecord, saveRecord, prepareCreateData, prepareUpdateData } = useUserOperations();
  
  return {
    createMerchant: (data: any) => createRecord('create-merchant', data),
    updateMerchant: (id: number, data: any) => updateRecord('update-merchant', id, data),
    saveMerchant: (data: any, isUpdate: boolean, id?: number) => 
      saveRecord('create-merchant', 'update-merchant', data, isUpdate, id),
    createMerchantBank: (data: any) => createRecord('create-merchant-bank', data),
    updateMerchantBank: (id: number, data: any) => updateRecord('update-merchant-bank', id, data),
    createMerchantWechat: (data: any) => createRecord('create-merchant-wechat', data),
    updateMerchantWechat: (id: number, data: any) => updateRecord('update-merchant-wechat', id, data),
    createMerchantUnionpay: (data: any) => createRecord('create-merchant-unionpay', data),
    updateMerchantUnionpay: (id: number, data: any) => updateRecord('update-merchant-unionpay', id, data),
    prepareCreateData,
    prepareUpdateData
  };
}

/**
 * Hook for bank operations with automatic user field injection
 */
export function useBankOperations() {
  const { createRecord, updateRecord, saveRecord, prepareCreateData, prepareUpdateData } = useUserOperations();
  
  return {
    createBank: (data: any) => createRecord('create-bank', data),
    updateBank: (id: number, data: any) => updateRecord('update-bank', id, data),
    saveBank: (data: any, isUpdate: boolean, id?: number) => 
      saveRecord('create-bank', 'update-bank', data, isUpdate, id),
    prepareCreateData,
    prepareUpdateData
  };
}

/**
 * Hook for user management operations with automatic user field injection
 */
export function useUserManagementOperations() {
  const { createRecord, updateRecord, prepareCreateData, prepareUpdateData } = useUserOperations();
  
  return {
    createUser: (data: any) => createRecord('create-user', data),
    updateUser: (id: number, data: any) => updateRecord('update-user', id, data),
    prepareCreateData,
    prepareUpdateData
  };
}
