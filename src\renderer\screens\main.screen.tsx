
import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Button } from '../components/button'
import { Modal } from '../components/modal'
import { useAuth } from '../contexts/AuthContext'
import { safeIpcInvoke } from '../utils/electron'

export function MainScreen() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { user, logout } = useAuth()

  const handleTestLogout = async () => {
    try {
      await safeIpcInvoke('clear-auth-state')
      await logout()
      console.log('Auth state cleared for testing')
    } catch (error) {
      console.error('Error clearing auth state:', error)
    }
  }

  return (
    <>
      <header className="flex flex-col items-center">
        <h1 className="title text-5xl">Epos Service App</h1>

        {/* <p className="text-lg text-muted-foreground">
          A react-router-dom adapter for Electron apps.
        </p> */}
        <p className="text-sm text-gray-600 mt-2">
          Welcome, {user?.user_name}! ({user?.role_name})
        </p>
        {/* <div className="flex gap-4 mt-4">
          <Link to="/todos" className="text-blue-500 hover:underline">View Todos</Link>
          <Link to="/modal-examples" className="text-blue-500 hover:underline">Modal Examples</Link>
        </div> */}
        {/* <Button className="mt-4" onClick={() => setIsModalOpen(true)}>Get Started</Button> */}
        {/* <Button className="mt-4 ml-4" variant="secondary" onClick={handleTestLogout}>Test Logout</Button> */}
        
        {/* <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
          <h2 className="text-2xl font-semibold mb-4">Getting Started Guide</h2>
          <p className="text-muted-foreground mb-4">
            Here's how to integrate Electron Router DOM in your project...
          </p>
          <Button onClick={() => setIsModalOpen(false)}>Close</Button>
        </Modal> */}
      </header>

      {/* <main className="mt-8 max-w-2xl mx-auto">
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold">Features</h2>
          <ul className="list-disc pl-5 space-y-2">
            <li>Seamless integration with react-router-dom</li>
            <li>Electron-specific routing capabilities</li>
            <li>Main process ↔ Renderer process communication</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-6">Getting Started</h2>
          <p className="text-muted-foreground">
            Import and use just like react-router-dom, with additional Electron-specific features.
          </p>
        </div>
      </main> */}
    </>
  )
}