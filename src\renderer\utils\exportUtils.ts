/**
 * Utility functions for exporting data to various formats
 */

/**
 * Convert array of objects to CSV format
 */
export function arrayToCSV<T extends Record<string, any>>(
  data: T[],
  headers?: Record<keyof T, string>
): string {
  if (data.length === 0) return '';

  const keys = Object.keys(data[0]) as (keyof T)[];
  
  // Use provided headers or default to keys
  const headerRow = headers 
    ? keys.map(key => headers[key] || String(key)).join(',')
    : keys.join(',');

  const rows = data.map(item => 
    keys.map(key => {
      const value = item[key];
      // Handle values that might contain commas, quotes, or newlines
      if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return String(value ?? '');
    }).join(',')
  );

  return [headerRow, ...rows].join('\n');
}

/**
 * Download data as CSV file
 */
export function downloadCSV<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: Record<keyof T, string>
): void {
  const csv = arrayToCSV(data, headers);
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

/**
 * Download data as JSON file
 */
export function downloadJSON<T>(
  data: T,
  filename: string
): void {
  const json = JSON.stringify(data, null, 2);
  const blob = new Blob([json], { type: 'application/json;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

/**
 * Format date for export
 */
export function formatDateForExport(date: string | Date | undefined): string {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('en-CA'); // YYYY-MM-DD format
}

/**
 * Format boolean for export
 */
export function formatBooleanForExport(value: boolean): string {
  return value ? 'Yes' : 'No';
}

/**
 * Bank-specific export headers
 */
export const BANK_EXPORT_HEADERS = {
  bank_id: 'ID',
  bank_code: 'Bank Code',
  bank_name_th: 'Bank Name (Thai)',
  bank_name_en: 'Bank Name (English)',
  bank_address_th: 'Address (Thai)',
  bank_address_en: 'Address (English)',
  active: 'Status',
  create_by: 'Created By',
  create_dt: 'Created Date',
  update_by: 'Updated By',
  update_dt: 'Updated Date'
};

/**
 * Merchant-specific export headers
 */
export const MERCHANT_EXPORT_HEADERS = {
  merchant_id: 'ID',
  merchant_code: 'Merchant Code',
  merchant_name_th: 'Merchant Name (Thai)',
  merchant_name_en: 'Merchant Name (English)',
  merchant_type: 'Type',
  active: 'Status',
  create_by: 'Created By',
  create_dt: 'Created Date',
  update_by: 'Updated By',
  update_dt: 'Updated Date'
};

/**
 * Network Service-specific export headers
 */
export const NETWORK_SERVICE_EXPORT_HEADERS = {
  service_id: 'ID',
  service_code: 'Service Code',
  service_name: 'Service Name',
  service_type: 'Type',
  active: 'Status',
  create_by: 'Created By',
  create_dt: 'Created Date',
  update_by: 'Updated By',
  update_dt: 'Updated Date'
};
