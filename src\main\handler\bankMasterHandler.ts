import { ipcMain } from 'electron';
import { Client } from 'pg';
import { getDbConnection } from '../db';

interface BankMaster {
  bank_id?: number;
  bank_code: string;
  bank_name_th?: string;
  bank_name_en?: string;
  bank_address_th?: string;
  bank_address_en?: string;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface BankMasterResponse {
  success: boolean;
  message: string;
  data?: BankMaster | BankMaster[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}

interface PaginationParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export function setupBankMasterHandlers() {
  console.log('Setting up Bank Master handlers...');

  // Get banks with pagination
  ipcMain.handle('get-banks', async (_event, params: PaginationParams = {}): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      const {
        page = 1,
        pageSize = 10,
        search = '',
        sortBy = 'bank_id',
        sortOrder = 'ASC'
      } = params;

      console.log(`📋 Fetching banks - Page: ${page}, Size: ${pageSize}, Search: "${search}"`);
      client = await getDbConnection();

      let whereClause = '';
      let searchParams: any[] = [];
      let paramIndex = 1;

      if (search.trim()) {
        whereClause = `WHERE (
          UPPER(bank_code) LIKE UPPER($${paramIndex}) OR
          UPPER(bank_name_th) LIKE UPPER($${paramIndex + 1}) OR
          UPPER(bank_name_en) LIKE UPPER($${paramIndex + 2})
        )`;
        searchParams = [`%${search}%`, `%${search}%`, `%${search}%`];
        paramIndex += 3;
      }

      const countQuery = `SELECT COUNT(*) as total FROM tmst_bank ${whereClause}`;
      const countResult = await client.query(countQuery, searchParams);
      const totalRecords = parseInt(countResult.rows[0].total);

      const totalPages = Math.ceil(totalRecords / pageSize);
      const offset = (page - 1) * pageSize;
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      const allowedSortColumns = ['bank_id', 'bank_code', 'bank_name_th', 'bank_name_en', 'active', 'create_dt', 'update_dt'];
      const validSortBy = allowedSortColumns.includes(sortBy) ? sortBy : 'bank_id';
      const validSortOrder = sortOrder === 'DESC' ? 'DESC' : 'ASC';

      const dataQuery = `
        SELECT bank_id, bank_code, bank_name_th, bank_name_en,
               bank_address_th, bank_address_en, active,
               create_by, create_dt, update_by, update_dt
        FROM tmst_bank
        ${whereClause}
        ORDER BY ${validSortBy} ${validSortOrder}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const dataParams = [...searchParams, pageSize, offset];
      const result = await client.query(dataQuery, dataParams);

      console.log(`✅ Found ${result.rows.length} banks (Page ${page}/${totalPages}, Total: ${totalRecords})`);

      return {
        success: true,
        message: `Found ${result.rows.length} banks on page ${page}`,
        data: result.rows,
        pagination: {
          currentPage: page,
          totalPages,
          totalRecords,
          pageSize,
          hasNextPage,
          hasPreviousPage
        }
      };

    } catch (error: any) {
      console.error('❌ Error fetching banks:', error.message);
      return {
        success: false,
        message: 'Failed to fetch banks',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Create bank
  ipcMain.handle('create-bank', async (_event, bankData: Omit<BankMaster, 'bank_id' | 'create_dt' | 'update_dt'>): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      console.log('➕ Creating new bank:', bankData.bank_name_th || bankData.bank_name_en);
      client = await getDbConnection();

      const query = `
        INSERT INTO tmst_bank (bank_code, bank_name_th, bank_name_en, bank_address_th, bank_address_en, active, create_by)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING bank_id, bank_code, bank_name_th, bank_name_en,
                  bank_address_th, bank_address_en, active,
                  create_by, create_dt, update_by, update_dt
      `;

      const values = [
        bankData.bank_code.toUpperCase(),
        bankData.bank_name_th || null,
        bankData.bank_name_en || null,
        bankData.bank_address_th || null,
        bankData.bank_address_en || null,
        bankData.active,
        bankData.create_by || 'SYSTEM'
      ];

      const result = await client.query(query, values);
      console.log('✅ Bank created successfully:', result.rows[0].bank_name_th || result.rows[0].bank_name_en);

      return {
        success: true,
        message: 'Bank created successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error creating bank:', error.message);

      let errorMessage = 'Failed to create bank';
      if (error.code === '23505') {
        errorMessage = 'Bank code already exists';
      } else if (error.message.includes('value too long')) {
        errorMessage = 'Bank code must be 5 characters or less';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Update bank
  ipcMain.handle('update-bank', async (_event, bankId: number, bankData: Omit<BankMaster, 'bank_id' | 'create_dt' | 'create_by'>): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      console.log('✏️ Updating bank ID:', bankId);
      client = await getDbConnection();

      const query = `
        UPDATE tmst_bank
        SET bank_code = $1, bank_name_th = $2, bank_name_en = $3,
            bank_address_th = $4, bank_address_en = $5, active = $6,
            update_by = $7, update_dt = CURRENT_TIMESTAMP
        WHERE bank_id = $8
        RETURNING bank_id, bank_code, bank_name_th, bank_name_en,
                  bank_address_th, bank_address_en, active,
                  create_by, create_dt, update_by, update_dt
      `;

      const values = [
        bankData.bank_code.toUpperCase(),
        bankData.bank_name_th || null,
        bankData.bank_name_en || null,
        bankData.bank_address_th || null,
        bankData.bank_address_en || null,
        bankData.active,
        bankData.update_by || 'SYSTEM',
        bankId
      ];

      const result = await client.query(query, values);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'Bank not found',
          error: 'BANK_NOT_FOUND'
        };
      }

      console.log('✅ Bank updated successfully:', result.rows[0].bank_name_th || result.rows[0].bank_name_en);

      return {
        success: true,
        message: 'Bank updated successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error updating bank:', error.message);

      let errorMessage = 'Failed to update bank';
      if (error.code === '23505') {
        errorMessage = 'Bank code already exists';
      } else if (error.message.includes('value too long')) {
        errorMessage = 'Bank code must be 5 characters or less';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Delete bank
  ipcMain.handle('delete-bank', async (_event, bankId: number): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      console.log('🗑️ Deleting bank ID:', bankId);
      client = await getDbConnection();

      const query = `
        DELETE FROM tmst_bank
        WHERE bank_id = $1
        RETURNING bank_name_th, bank_name_en
      `;

      const result = await client.query(query, [bankId]);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'Bank not found',
          error: 'BANK_NOT_FOUND'
        };
      }

      console.log('✅ Bank deleted successfully:', result.rows[0].bank_name_th || result.rows[0].bank_name_en);

      return {
        success: true,
        message: 'Bank deleted successfully'
      };

    } catch (error: any) {
      console.error('❌ Error deleting bank:', error.message);
      return {
        success: false,
        message: 'Failed to delete bank',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });
}
