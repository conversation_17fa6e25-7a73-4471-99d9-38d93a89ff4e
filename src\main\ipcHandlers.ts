// Import all handler modules
import { setupTodoHandlers } from './handler/todoHandler';
import { setupCsvHandlers } from './handler/csvHandler';
import { setupConnectionHandlers } from './handler/connectionHandler';
import { setupBankMasterHandlers } from './handler/bankMasterHandler';
import { setupAuthHandlers } from './handler/authHandler';
import { setupSystemHandlers } from './handler/systemHandler';
import { setupCompanySettingsHandlers } from './handler/companySettingsHandler';
import { setupNetworkServiceHandlers } from './handler/networkServiceHandler';
import { setupMerchantHandlers } from './handler/merchantHandler';
import { setupMasterDataHandlers } from './handler/masterDataHandler';
import { setupTransactionHandlers } from './handler/transactionHandler';
import { setupNotificationHandlers } from './handler/notificationHandler';
import { setupSystemTrayHandlers } from './handler/systemTrayHandler';

export function setupIpcHandlers() {
  console.log('Setting up IPC handlers...');

  // Call all handler setup functions
  setupTodoHandlers();
  setupCsvHandlers();
  setupConnectionHandlers();
  setupBankMasterHandlers();
  setupAuthHandlers();
  setupSystemHandlers();
  setupCompanySettingsHandlers();
  setupNetworkServiceHandlers();
  setupMerchantHandlers();
  setupMasterDataHandlers();
  setupTransactionHandlers();
  setupNotificationHandlers();
  setupSystemTrayHandlers();
}