import { safeIpcInvoke } from '../utils/electron';

export interface TransactionSummaryData {
  merchantVat: string;
  merchantName: string;
  transactionDate: string;
  transactionCount: number;
  totalAmount: number;
  mdrRate: number;
  mdrAmount: number;
  vatPercentage: number;
  vatAmount: number;
  netAmount: number;
  withholdingTaxRate: number;
  withholdTax: number;
  transferFee: number;
  reimbursementFee: number;
  serviceFee: number;
  finalNetAmount: number;
  cupBusinessTaxFee: number;
  channelType: string;
  tradeStatus: 'SUCCESS' | 'REFUND'; // New field to identify row type
  bankCode: string; // Bank code for grouping transfers
  bankNameTh: string; // Thai bank name
  bankNameEn: string; // English bank name
}

export interface BankSubtotal {
  bankCode: string;
  bankNameTh: string;
  bankNameEn: string;
  merchants: TransactionSummaryData[];
  subtotals: {
    totalTransactions: number;
    totalAmount: number;
    totalMdrAmount: number;
    totalVatAmount: number;
    totalNetAmount: number;
    totalWithholdTax: number;
    totalTransferFee: number;
    totalReimbursementFee: number;
    totalServiceFee: number;
    totalFinalNetAmount: number;
    totalCupBusinessTaxFee: number;
  };
}

export interface ReportSummary {
  merchants: TransactionSummaryData[];
  bankSubtotals: BankSubtotal[]; // New field for bank-wise grouping
  grandTotals: {
    totalTransactions: number;
    totalAmount: number;
    totalMdrAmount: number;
    totalVatAmount: number;
    totalNetAmount: number;
    totalWithholdTax: number;
    totalTransferFee: number;
    totalReimbursementFee: number;
    totalServiceFee: number;
    totalFinalNetAmount: number;
    totalCupBusinessTaxFee: number;
    averageMdrRate: number;
    averageVatPercentage: number;
    averageWithholdingTaxRate: number;
  };
  reportDate: string;
  reportTime: string;
  pageCount: number;
}

export class ReportService {
  /**
   * Round a number to 2 decimal places to avoid floating point precision issues
   */
  private static roundToTwoDecimals(num: number): number {
    return Math.round(num * 100) / 100;
  }

  /**
   * Generate bank subtotals from merchant data for easy transfer processing
   */
  private static generateBankSubtotals(merchants: TransactionSummaryData[]): BankSubtotal[] {
    // Group merchants by bank code
    const bankGroups = new Map<string, TransactionSummaryData[]>();

    merchants.forEach(merchant => {
      const bankCode = merchant.bankCode || 'UNKNOWN';
      if (!bankGroups.has(bankCode)) {
        bankGroups.set(bankCode, []);
      }
      bankGroups.get(bankCode)!.push(merchant);
    });

    // Create bank subtotals
    const bankSubtotals: BankSubtotal[] = [];

    for (const [bankCode, bankMerchants] of bankGroups) {
      const firstMerchant = bankMerchants[0];

      // Calculate subtotals for this bank
      const subtotals = {
        totalTransactions: bankMerchants.reduce((sum, m) => sum + m.transactionCount, 0),
        totalAmount: this.roundToTwoDecimals(bankMerchants.reduce((sum, m) => sum + m.totalAmount, 0)),
        totalMdrAmount: this.roundToTwoDecimals(bankMerchants.reduce((sum, m) => sum + m.mdrAmount, 0)),
        totalVatAmount: this.roundToTwoDecimals(bankMerchants.reduce((sum, m) => sum + m.vatAmount, 0)),
        totalNetAmount: this.roundToTwoDecimals(bankMerchants.reduce((sum, m) => sum + m.netAmount, 0)),
        totalWithholdTax: this.roundToTwoDecimals(bankMerchants.reduce((sum, m) => sum + m.withholdTax, 0)),
        totalTransferFee: this.roundToTwoDecimals(bankMerchants.reduce((sum, m) => sum + m.transferFee, 0)),
        totalReimbursementFee: this.roundToTwoDecimals(bankMerchants.reduce((sum, m) => sum + m.reimbursementFee, 0)),
        totalServiceFee: this.roundToTwoDecimals(bankMerchants.reduce((sum, m) => sum + m.serviceFee, 0)),
        totalFinalNetAmount: this.roundToTwoDecimals(bankMerchants.reduce((sum, m) => sum + m.finalNetAmount, 0)),
        totalCupBusinessTaxFee: this.roundToTwoDecimals(bankMerchants.reduce((sum, m) => sum + m.cupBusinessTaxFee, 0))
      };

      bankSubtotals.push({
        bankCode,
        bankNameTh: firstMerchant.bankNameTh || 'Unknown Bank',
        bankNameEn: firstMerchant.bankNameEn || 'Unknown Bank',
        merchants: bankMerchants,
        subtotals
      });
    }

    // Sort by bank code for consistent ordering
    return bankSubtotals.sort((a, b) => a.bankCode.localeCompare(b.bankCode));
  }

  /**
   * Generate bank-grouped data with bank totals after each bank's merchants
   */
  static generateDateGroupedData(merchants: TransactionSummaryData[]): any[] {
    // Group merchants by bank code
    const bankGroups = new Map<string, TransactionSummaryData[]>();

    for (const merchant of merchants) {
      const bankCode = merchant.bankCode || 'UNKNOWN';
      if (!bankGroups.has(bankCode)) {
        bankGroups.set(bankCode, []);
      }
      bankGroups.get(bankCode)!.push(merchant);
    }

    // Create bank-grouped structure with bank totals after each bank
    const bankGroupedData: any[] = [];

    // Sort bank codes
    const sortedBankCodes = Array.from(bankGroups.keys()).sort();

    for (const bankCode of sortedBankCodes) {
      const bankMerchants = bankGroups.get(bankCode)!;

      // Add individual merchant rows for this bank
      bankGroupedData.push(...bankMerchants);

      // Add bank total immediately after the bank's merchants
      const bankTotal = {
        isSubtotal: true,
        subtotalType: 'bank',
        bankCode,
        bankNameEn: bankMerchants[0]?.bankNameEn || 'Unknown Bank',
        totalTransactions: bankMerchants.reduce((sum, m) => sum + (m.transactionCount || 0), 0),
        totalAmount: bankMerchants.reduce((sum, m) => sum + (m.totalAmount || 0), 0),
        totalMdrAmount: bankMerchants.reduce((sum, m) => sum + (m.mdrAmount || 0), 0),
        totalVatAmount: bankMerchants.reduce((sum, m) => sum + (m.vatAmount || 0), 0),
        totalTransferFee: bankMerchants.reduce((sum, m) => sum + (m.transferFee || 0), 0),
        totalNetAmount: bankMerchants.reduce((sum, m) => sum + (m.netAmount || 0), 0),
        totalWithholdTax: bankMerchants.reduce((sum, m) => sum + (m.withholdTax || 0), 0),
        totalReimbursementFee: bankMerchants.reduce((sum, m) => sum + (m.reimbursementFee || 0), 0),
        totalServiceFee: bankMerchants.reduce((sum, m) => sum + (m.serviceFee || 0), 0),
        totalFinalNetAmount: bankMerchants.reduce((sum, m) => sum + (m.finalNetAmount || 0), 0),
        totalBusinessTax: bankMerchants.reduce((sum, m) => sum + (m.cupBusinessTaxFee || 0), 0)
      };

      bankGroupedData.push(bankTotal);
    }

    // Add empty space row before grand total for visual separation
    const emptyRow = {
      isSubtotal: true,
      subtotalType: 'empty'
    };
    bankGroupedData.push(emptyRow);

    // Calculate and add final grand total
    const finalTotal = {
      isSubtotal: true,
      subtotalType: 'grand',
      totalTransactions: merchants.reduce((sum, m) => sum + (m.transactionCount || 0), 0),
      totalAmount: merchants.reduce((sum, m) => sum + (m.totalAmount || 0), 0),
      totalMdrAmount: merchants.reduce((sum, m) => sum + (m.mdrAmount || 0), 0),
      totalVatAmount: merchants.reduce((sum, m) => sum + (m.vatAmount || 0), 0),
      totalTransferFee: merchants.reduce((sum, m) => sum + (m.transferFee || 0), 0),
      totalNetAmount: merchants.reduce((sum, m) => sum + (m.netAmount || 0), 0),
      totalWithholdTax: merchants.reduce((sum, m) => sum + (m.withholdTax || 0), 0),
      totalReimbursementFee: merchants.reduce((sum, m) => sum + (m.reimbursementFee || 0), 0),
      totalServiceFee: merchants.reduce((sum, m) => sum + (m.serviceFee || 0), 0),
      totalFinalNetAmount: merchants.reduce((sum, m) => sum + (m.finalNetAmount || 0), 0),
      totalBusinessTax: merchants.reduce((sum, m) => sum + (m.cupBusinessTaxFee || 0), 0)
    };
    bankGroupedData.push(finalTotal);

    return bankGroupedData;
  }

  /**
   * Generate bank totals for the new layout
   */
  static generateBankTotals(merchants: TransactionSummaryData[]): any[] {
    const bankGroups = new Map<string, TransactionSummaryData[]>();

    // Group merchants by bank code
    for (const merchant of merchants) {
      const bankCode = merchant.bankCode || 'UNKNOWN';
      if (!bankGroups.has(bankCode)) {
        bankGroups.set(bankCode, []);
      }
      bankGroups.get(bankCode)!.push(merchant);
    }

    // Calculate totals for each bank
    const bankTotals = [];
    for (const [bankCode, bankMerchants] of bankGroups) {
      const bankTotal = {
        isSubtotal: true,
        subtotalType: 'bank',
        bankCode,
        bankNameEn: bankMerchants[0]?.bankNameEn || 'Unknown Bank',
        totalTransactions: bankMerchants.reduce((sum, m) => sum + (m.transactionCount || 0), 0),
        totalAmount: bankMerchants.reduce((sum, m) => sum + (m.totalAmount || 0), 0),
        totalMdrAmount: bankMerchants.reduce((sum, m) => sum + (m.mdrAmount || 0), 0),
        totalVatAmount: bankMerchants.reduce((sum, m) => sum + (m.vatAmount || 0), 0),
        totalNetAmount: bankMerchants.reduce((sum, m) => sum + (m.netAmount || 0), 0),
        totalWithholdTax: bankMerchants.reduce((sum, m) => sum + (m.withholdTax || 0), 0),
        totalReimbursementFee: bankMerchants.reduce((sum, m) => sum + (m.reimbursementFee || 0), 0),
        totalServiceFee: bankMerchants.reduce((sum, m) => sum + (m.serviceFee || 0), 0),
        totalFinalNetAmount: bankMerchants.reduce((sum, m) => sum + (m.finalNetAmount || 0), 0),
        totalBusinessTax: bankMerchants.reduce((sum, m) => sum + (m.cupBusinessTaxFee || 0), 0)
      };

      bankTotals.push(bankTotal);
    }

    return bankTotals.sort((a, b) => a.bankCode.localeCompare(b.bankCode));
  }

  /**
   * Create a merchant summary row for either SUCCESS or REFUND transactions
   */
  private static createMerchantSummaryRow(
    vatNumber: string,
    firstTransaction: any,
    transactionCount: number,
    totalAmount: number,
    mdrRate: number,
    vatPercentage: number,
    withholdingTaxRate: number,
    baseTransferFee: number,
    tradeStatus: 'SUCCESS' | 'REFUND'
  ): TransactionSummaryData {
    // For refunds, amounts should be negative
    const isRefund = tradeStatus === 'REFUND';
    const amount = Math.abs(totalAmount);
    const signedAmount = isRefund ? -amount : amount;

    // Financial calculations
    const mdrAmount = this.roundToTwoDecimals(signedAmount * (mdrRate / 100));
    const transferFeeAmount = isRefund ? -baseTransferFee : baseTransferFee;

    // VAT calculation: (Transfer Fee + MDR Amount) × VAT%
    const vatOnTransferFee = this.roundToTwoDecimals(transferFeeAmount * (vatPercentage / 100));
    const vatOnMdrAmount = this.roundToTwoDecimals(mdrAmount * (vatPercentage / 100));
    const vatAmount = this.roundToTwoDecimals(vatOnTransferFee + vatOnMdrAmount);

    // Net Amount = Transaction Amount - (MDR Amount + VAT Amount)
    const netAmount = this.roundToTwoDecimals(signedAmount - (mdrAmount + vatAmount));

    // Withholding Tax = MDR Amount × Withholding Tax%
    const withholdTax = this.roundToTwoDecimals(mdrAmount * (withholdingTaxRate / 100));

    // Reimbursement Fee = Transaction Amount × 0.5%
    const reimbursementFee = this.roundToTwoDecimals(signedAmount * 0.005);

    // Additional fees based on channel type
    const channelType = firstTransaction?.transaction_channel_type?.toLowerCase() || '';
    const serviceFee = channelType === 'wechat' ? 0 : this.roundToTwoDecimals(signedAmount * 0.001); // 0% for WeChat, 0.1% for others
    const cupBusinessTaxFee = channelType === 'wechat' ? 0 : this.roundToTwoDecimals(signedAmount * 0.0005); // 0% for WeChat, 0.05% for others

    // Final Net Amount = Total Amount - (Transfer Fee + Reimbursement Fee + Service Fee + CUP Business Tax)
    const finalNetAmount = this.roundToTwoDecimals(signedAmount - transferFeeAmount - reimbursementFee - serviceFee - cupBusinessTaxFee);

    return {
      merchantVat: vatNumber,
      merchantName: firstTransaction?.transaction_merchant_name || 'Unknown Merchant',
      transactionDate: firstTransaction?.transaction_time ? this.formatDate(firstTransaction.transaction_time) : 'N/A',
      transactionCount,
      totalAmount: signedAmount,
      mdrRate,
      mdrAmount,
      vatPercentage,
      vatAmount,
      netAmount,
      withholdingTaxRate,
      withholdTax,
      transferFee: transferFeeAmount,
      reimbursementFee,
      serviceFee,
      finalNetAmount,
      cupBusinessTaxFee,
      channelType: firstTransaction?.transaction_channel_type || 'Unknown',
      tradeStatus,
      bankCode: firstTransaction?.bank_code || 'UNKNOWN',
      bankNameTh: firstTransaction?.bank_name_th || 'Unknown Bank',
      bankNameEn: firstTransaction?.bank_name_en || 'Unknown Bank'
    };
  }

  /**
   * Enhanced Financial Calculation Methodology:
   * • MDR Amount = Transaction Amount × (MDR% ÷ 100) [from merchant_wechat.wechat_rate]
   * • Transfer Fee = merchant.transfer_fee [from merchant.transfer_fee]
   * • VAT Amount = (merchant.transfer_fee × VAT% ÷ 100) + (MDR Amount × (VAT% ÷ 100)) [from network_service.vat_percentage]
   * • Net Amount = Transaction Amount - (MDR Amount + VAT Amount)
   * • Withholding Tax = MDR Amount × (Withholding Tax% ÷ 100) [from merchant.withholding_tax]
   * • Reimbursement Fee = Transaction Amount × 0.5%
   * • Final Net Amount = Total Amount - (Transfer Fee + Reimbursement Fee + Service Fee + CUP Business Tax)
   */

  static async generateTransactionSummaryReport(filters: any): Promise<ReportSummary> {
    try {
      console.log('📊 Generating transaction summary report with filters:', filters);

      // Get enhanced transaction data with financial calculations
      const result = await safeIpcInvoke('get-enhanced-transactions-for-report', {
        ...filters,
        page: 1,
        pageSize: 10000, // Get all transactions for report
        sortBy: 'transaction_merchant_vat',
        sortOrder: 'ASC'
      });

      if (!result.success || !result.data) {
        throw new Error('Failed to fetch enhanced transaction data for report');
      }

      const transactions = result.data;
      console.log(`📊 Processing ${transactions.length} enhanced transactions for summary report`);

      // Group transactions by merchant VAT
      const merchantGroups = new Map<string, any[]>();
      
      transactions.forEach((transaction: any) => {
        try {
          const vatNumber = transaction.transaction_merchant_vat || 'NO_VAT';
          if (!merchantGroups.has(vatNumber)) {
            merchantGroups.set(vatNumber, []);
          }
          merchantGroups.get(vatNumber)!.push(transaction);
        } catch (error) {
          console.warn('⚠️ Error processing transaction:', transaction, error);
        }
      });

      // Process each merchant group
      const merchants: TransactionSummaryData[] = [];
      let grandTotals = {
        totalTransactions: 0,
        totalAmount: 0,
        totalMdrAmount: 0,
        totalVatAmount: 0,
        totalNetAmount: 0,
        totalWithholdTax: 0,
        totalTransferFee: 0,
        totalReimbursementFee: 0,
        totalServiceFee: 0,
        totalFinalNetAmount: 0,
        totalCupBusinessTaxFee: 0,
        averageMdrRate: 0,
        averageVatPercentage: 0,
        averageWithholdingTaxRate: 0
      };

      // Track totals for average calculations
      let totalMdrRateSum = 0;
      let totalVatPercentageSum = 0;
      let totalWithholdingTaxRateSum = 0;

      // Sort merchants by VAT number
      const sortedMerchants = Array.from(merchantGroups.entries()).sort(([a], [b]) => a.localeCompare(b));

      sortedMerchants.forEach(([vatNumber, merchantTransactions]) => {
        try {
          const firstTransaction = merchantTransactions[0];

          // Separate success and refund transactions
          const successTransactions = merchantTransactions.filter(t =>
            (t.transaction_trade_status || '').toLowerCase() === 'success'
          );
          const refundTransactions = merchantTransactions.filter(t =>
            (t.transaction_trade_status || '').toLowerCase() === 'refund'
          );

          // Use actual database values for financial calculations
          const mdrRate = parseFloat(firstTransaction.wechat_rate) || 0; // From merchant_wechat table
          const vatPercentage = parseFloat(firstTransaction.vat_percentage) || 0; // From network_service table
          const withholdingTaxRate = parseFloat(firstTransaction.withholding_tax) || 0; // From merchant table
          const baseTransferFee = parseFloat(firstTransaction.transfer_fee) || 0; // From merchant table

          // Create SUCCESS row if there are success transactions
          if (successTransactions.length > 0) {
            const successAmount = successTransactions.reduce((sum, t) => {
              const amount = parseFloat(t.transaction_amount) || 0;
              return sum + Math.abs(amount);
            }, 0);

            const successMerchantSummary = this.createMerchantSummaryRow(
              vatNumber,
              firstTransaction,
              successTransactions.length,
              successAmount,
              mdrRate,
              vatPercentage,
              withholdingTaxRate,
              baseTransferFee,
              'SUCCESS'
            );

            merchants.push(successMerchantSummary);

            // Add to grand totals
            grandTotals.totalTransactions += successTransactions.length;
            grandTotals.totalAmount = this.roundToTwoDecimals(grandTotals.totalAmount + successMerchantSummary.totalAmount);
            grandTotals.totalMdrAmount = this.roundToTwoDecimals(grandTotals.totalMdrAmount + successMerchantSummary.mdrAmount);
            grandTotals.totalVatAmount = this.roundToTwoDecimals(grandTotals.totalVatAmount + successMerchantSummary.vatAmount);
            grandTotals.totalNetAmount = this.roundToTwoDecimals(grandTotals.totalNetAmount + successMerchantSummary.netAmount);
            grandTotals.totalWithholdTax = this.roundToTwoDecimals(grandTotals.totalWithholdTax + successMerchantSummary.withholdTax);
            grandTotals.totalTransferFee = this.roundToTwoDecimals(grandTotals.totalTransferFee + successMerchantSummary.transferFee);
            grandTotals.totalReimbursementFee = this.roundToTwoDecimals(grandTotals.totalReimbursementFee + successMerchantSummary.reimbursementFee);
            grandTotals.totalServiceFee = this.roundToTwoDecimals(grandTotals.totalServiceFee + successMerchantSummary.serviceFee);
            grandTotals.totalFinalNetAmount = this.roundToTwoDecimals(grandTotals.totalFinalNetAmount + successMerchantSummary.finalNetAmount);
            grandTotals.totalCupBusinessTaxFee = this.roundToTwoDecimals(grandTotals.totalCupBusinessTaxFee + successMerchantSummary.cupBusinessTaxFee);

            // Add to rate sums for average calculations
            totalMdrRateSum += mdrRate;
            totalVatPercentageSum += vatPercentage;
            totalWithholdingTaxRateSum += withholdingTaxRate;
          }

          // Create REFUND row if there are refund transactions
          if (refundTransactions.length > 0) {
            const refundAmount = refundTransactions.reduce((sum, t) => {
              const amount = parseFloat(t.transaction_amount) || 0;
              return sum + Math.abs(amount);
            }, 0);

            const refundMerchantSummary = this.createMerchantSummaryRow(
              vatNumber,
              firstTransaction,
              refundTransactions.length,
              -refundAmount, // Negative amount for refunds
              mdrRate,
              vatPercentage,
              withholdingTaxRate,
              baseTransferFee,
              'REFUND'
            );

            merchants.push(refundMerchantSummary);

            // Add to grand totals (refunds will reduce totals)
            grandTotals.totalTransactions += refundTransactions.length;
            grandTotals.totalAmount = this.roundToTwoDecimals(grandTotals.totalAmount + refundMerchantSummary.totalAmount);
            grandTotals.totalMdrAmount = this.roundToTwoDecimals(grandTotals.totalMdrAmount + refundMerchantSummary.mdrAmount);
            grandTotals.totalVatAmount = this.roundToTwoDecimals(grandTotals.totalVatAmount + refundMerchantSummary.vatAmount);
            grandTotals.totalNetAmount = this.roundToTwoDecimals(grandTotals.totalNetAmount + refundMerchantSummary.netAmount);
            grandTotals.totalWithholdTax = this.roundToTwoDecimals(grandTotals.totalWithholdTax + refundMerchantSummary.withholdTax);
            grandTotals.totalTransferFee = this.roundToTwoDecimals(grandTotals.totalTransferFee + refundMerchantSummary.transferFee);
            grandTotals.totalReimbursementFee = this.roundToTwoDecimals(grandTotals.totalReimbursementFee + refundMerchantSummary.reimbursementFee);
            grandTotals.totalServiceFee = this.roundToTwoDecimals(grandTotals.totalServiceFee + refundMerchantSummary.serviceFee);
            grandTotals.totalFinalNetAmount = this.roundToTwoDecimals(grandTotals.totalFinalNetAmount + refundMerchantSummary.finalNetAmount);
            grandTotals.totalCupBusinessTaxFee = this.roundToTwoDecimals(grandTotals.totalCupBusinessTaxFee + refundMerchantSummary.cupBusinessTaxFee);

            // Add to rate sums for average calculations
            totalMdrRateSum += mdrRate;
            totalVatPercentageSum += vatPercentage;
            totalWithholdingTaxRateSum += withholdingTaxRate;
          }


        } catch (error) {
          console.warn('⚠️ Error processing merchant group:', vatNumber, error);
        }
      });

      // Calculate averages
      const merchantCount = merchants.length;
      if (merchantCount > 0) {
        grandTotals.averageMdrRate = totalMdrRateSum / merchantCount;
        grandTotals.averageVatPercentage = totalVatPercentageSum / merchantCount;
        grandTotals.averageWithholdingTaxRate = totalWithholdingTaxRateSum / merchantCount;
      }

      // Generate bank subtotals
      const bankSubtotals = this.generateBankSubtotals(merchants);

      const now = new Date();
      const reportSummary: ReportSummary = {
        merchants,
        bankSubtotals,
        grandTotals,
        reportDate: this.formatDate(now),
        reportTime: this.formatTime(now),
        pageCount: Math.ceil(merchants.length / 20) // 20 merchants per page
      };

      console.log(`📊 Generated summary report for ${merchants.length} merchants`);
      return reportSummary;

    } catch (error) {
      console.error('❌ Error generating transaction summary report:', error);
      throw error;
    }
  }

  static formatDate(date: string | Date): string {
    const d = new Date(date);
    const day = d.getDate().toString().padStart(2, '0');
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  }

  static formatTime(date: Date): string {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  }

  static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
      minimumFractionDigits: 2
    }).format(amount);
  }

  static formatNumber(num: number): string {
    return new Intl.NumberFormat('th-TH', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num);
  }

  static formatPercentage(num: number): string {
    return new Intl.NumberFormat('th-TH', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num / 100);
  }
}
