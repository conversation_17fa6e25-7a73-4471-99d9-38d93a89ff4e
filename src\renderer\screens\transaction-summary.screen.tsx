import { useState } from 'react';
import { Button } from '../components/button';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../contexts/NotificationContext';
import { safeIpcInvoke } from '../utils/electron';
import { RoleBasedComponent } from '../components/RoleBasedComponent';

// Types for Transaction Summary
interface TransactionSummaryItem {
  id: number;
  bank: string;
  group: string;
  merchantId: string;
  accountNo: string;
  accountName: string;
  amount: number;
  fax?: string;
  email?: string;
  isTransfer?: number; // 0 = not yet transferred, 1 = transfer already
}

interface TransactionSummaryData {
  runningNumber: string;
  date: string;
  transferToday: number;
  pendingTransfer: number;
  totalAmount: number;
  items: TransactionSummaryItem[];
}

interface TransactionDetailItem {
  id: number;
  trnDate: string;
  serviceAC: string;
  mid: string;
  name: string;
  tid: string;
  trnAmt: number;
  sumMDR: number;
  vat: number;
  netAmt: number;
  withholdTax: number;
  reimbursementFee: number;
  serviceFee: number;
  netAmtCUP: number;
  businessTax: number;
  tidNo: number;
}

interface TransferTodayItem {
  id: number;
  merchant_vat: string;
  merchant_name: string;
  transaction_date: string;
  channel_type: string;
  transaction_count: number;
  total_amount: number;
  transfer_fee: number;
  final_net_amount: number;
  is_transfer: number;
  create_by: string;
  create_dt: string;
  update_by: string;
  update_dt: string;
  report_date: string;
  running_number: string;
  batch_id: string;
}

export function TransactionSummaryScreen() {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  // const { hasWriteAccess } = useMasterDataAccess(); // Not used in current implementation
  
  const [summaryData, setSummaryData] = useState<TransactionSummaryData | null>(null);
  const [transactionDetails, setTransactionDetails] = useState<TransactionDetailItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [runningNumber, setRunningNumber] = useState('2507013');
  const [showTransferDetails, setShowTransferDetails] = useState(false);
  const [activeTab, setActiveTab] = useState<'summary' | 'transaction' | 'transfer-today'>('summary');
  const [transferTodayData, setTransferTodayData] = useState<TransferTodayItem[]>([]);
  const [transferTodayLoading, setTransferTodayLoading] = useState(false);

  // Mock data for demonstration
  const mockSummaryData: TransactionSummaryData = {
    runningNumber: '2507013',
    date: '19/07/2005',
    transferToday: 773774.52,
    pendingTransfer: 0.00,
    totalAmount: 773774.52,
    items: [
      {
        id: 1,
        bank: '002 ธนาคารกรุงเทพ จำกัด (มหาชน)',
        group: 'OTHER',
        merchantId: '****************',
        accountNo: '**********',
        accountName: 'T Z W CO LTD',
        amount: 209426.49,
        fax: '',
        email: ''
      },
      {
        id: 2,
        bank: '002 ธนาคารกรุงเทพ จำกัด (มหาชน)',
        group: 'SG',
        merchantId: '****************',
        accountNo: '**********',
        accountName: 'P.S. SIAM GARDEN CO.,LTD.',
        amount: 149179.48,
        fax: '',
        email: ''
      },
      {
        id: 3,
        bank: '002 ธนาคารกรุงเทพ จำกัด (มหาชน)',
        group: 'SG',
        merchantId: '****************',
        accountNo: '**********',
        accountName: 'S.G CENTER CO.,LTD',
        amount: 409285.25,
        fax: '02-4499520',
        email: ''
      },
      {
        id: 4,
        bank: '002 ธนาคารกรุงเทพ จำกัด (มหาชน)',
        group: 'SG',
        merchantId: '****************',
        accountNo: '**********',
        accountName: 'P.P. SIAM RETAIL CO.,LTD',
        amount: 5332.50,
        fax: '038-421621',
        email: ''
      },
      {
        id: 5,
        bank: '025 ธนาคารกรุงไทย จำกัด (มหาชน)',
        group: 'OTHER',
        merchantId: '****************',
        accountNo: '**********',
        accountName: 'Wirthaya Rattanastat',
        amount: 4550.76,
        fax: '',
        email: ''
      }
    ]
  };

  // Mock transaction detail data
  const mockTransactionDetails: TransactionDetailItem[] = [
    {
      id: 1,
      trnDate: '17/07/2005',
      serviceAC: 'M5002567',
      mid: '*************',
      name: 'P.S. SIAM GARDEN CO.,LTD.',
      tid: '0.50',
      trnAmt: 60630.00,
      sumMDR: 1295.67,
      vat: 94.50,
      netAmt: 143179.43,
      withholdTax: 40.57,
      reimbursementFee: 759.15,
      serviceFee: 0.00,
      netAmtCUP: 140976.85,
      businessTax: 0.00,
      tidNo: 1
    },
    {
      id: 2,
      trnDate: '17/07/2005',
      serviceAC: 'M5002567',
      mid: '*************',
      name: 'P.S. SIAM GARDEN CO.,LTD.',
      tid: '0.50',
      trnAmt: 413500.00,
      sumMDR: 3715.39,
      vat: 263.50,
      netAmt: 409285.25,
      withholdTax: 11.48,
      reimbursementFee: 2146.93,
      serviceFee: 0.00,
      netAmtCUP: 411398.10,
      businessTax: 0.00,
      tidNo: 1
    },
    {
      id: 3,
      trnDate: '17/07/2005',
      serviceAC: 'M4903826',
      mid: '6689054400003',
      name: 'P.P. SIAM RETAIL CO.,LTD',
      tid: '1.30',
      trnAmt: 3600.00,
      sumMDR: 46.80,
      vat: 3.28,
      netAmt: 3549.92,
      withholdTax: 1.40,
      reimbursementFee: 36.50,
      serviceFee: 3.60,
      netAmtCUP: 3505.90,
      businessTax: 0.54,
      tidNo: 1
    }
  ];

  // useEffect(() => {
  //   if (hasReadAccess) {
  //     loadSummaryData();
  //   }
  // }, [hasReadAccess, selectedDate]);

  // Function to load transfer today data (pending transfers)
  const loadTransferTodayData = async () => {
    setTransferTodayLoading(true);
    try {
      // Get all pending transfers (is_transfer = 0)
      const result = await safeIpcInvoke('get-transfer-status-report', {
        isTransfer: 0, // Only pending transfers
        page: 1,
        pageSize: 1000 // Get all pending transfers
      });

      if (result.success) {
        setTransferTodayData(result.data || []);
      } else {
        showNotification('Failed to load transfer today data: ' + (result.error || 'Unknown error'), 'error');
        setTransferTodayData([]);
      }
    } catch (error) {
      console.error('Error loading transfer today data:', error);
      showNotification('Error loading transfer today data', 'error');
      setTransferTodayData([]);
    } finally {
      setTransferTodayLoading(false);
    }
  };

  // Function to mark transfers as completed
  const markTransfersAsCompleted = async (detailIds: number[]) => {
    if (!user?.user_name) {
      showNotification('User information not available', 'error');
      return;
    }

    try {
      const result = await safeIpcInvoke('bulk-update-transfer-status', {
        detailIds,
        isTransfer: 1, // Mark as transferred
        updatedBy: user.user_name
      });

      if (result.success) {
        showNotification(`Successfully marked ${result.updatedCount} transfers as completed`, 'success');
        // Reload transfer today data
        await loadTransferTodayData();
      } else {
        showNotification('Failed to update transfer status: ' + (result.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error updating transfer status:', error);
      showNotification('Error updating transfer status', 'error');
    }
  };

  const loadSummaryData = async () => {
    setLoading(true);
    try {
      // Get transaction summary reports from database
      const result = await safeIpcInvoke('get-transaction-summary-reports', {
        startDate: selectedDate,
        endDate: selectedDate,
        status: 'GENERATED',
        page: 1,
        pageSize: 100
      });

      if (result.success && result.data.length > 0) {
        // Use the most recent report for the selected date
        const latestReport = result.data[0];

        // Get detailed data for this report
        const detailResult = await safeIpcInvoke('get-transaction-summary-report-details', latestReport.id);

        if (detailResult.success) {
          const reportData = detailResult.data;

          // Convert database data to UI format
          const summaryItems = reportData.details.map((detail: any, index: number) => ({
            id: index + 1,
            bank: '002 ธนาคารกรุงเทพ จำกัด (มหาชน)', // Default bank for now
            group: 'SG', // Default group for now
            merchantId: detail.merchant_vat,
            accountNo: '**********', // Default account for now
            accountName: detail.merchant_name,
            amount: parseFloat(detail.final_net_amount),
            fax: '',
            email: '',
            isTransfer: detail.is_transfer || 0
          }));

          const convertedSummaryData = {
            runningNumber: reportData.report.running_number || runningNumber,
            date: formatDate(reportData.report.report_date),
            transferToday: parseFloat(reportData.report.grand_total_final_net_amount),
            pendingTransfer: 0.00,
            totalAmount: parseFloat(reportData.report.grand_total_amount),
            items: summaryItems
          };

          // Convert to transaction details format
          const transactionDetailsData = reportData.details.map((detail: any, index: number) => ({
            id: index + 1,
            trnDate: formatDate(detail.transaction_date),
            serviceAC: 'M5002567', // Default service account
            mid: detail.merchant_vat,
            name: detail.merchant_name,
            tid: detail.mdr_rate.toString(),
            trnAmt: parseFloat(detail.total_amount),
            sumMDR: parseFloat(detail.mdr_amount),
            vat: parseFloat(detail.vat_amount),
            netAmt: parseFloat(detail.net_amount),
            withholdTax: parseFloat(detail.withhold_tax),
            reimbursementFee: parseFloat(detail.reimbursement_fee),
            serviceFee: parseFloat(detail.service_fee),
            netAmtCUP: parseFloat(detail.final_net_amount),
            businessTax: parseFloat(detail.cup_business_tax_fee),
            tidNo: 1
          }));

          setSummaryData(convertedSummaryData);
          setTransactionDetails(transactionDetailsData);
        } else {
          // Fallback to mock data if no details found
          setSummaryData(mockSummaryData);
          setTransactionDetails(mockTransactionDetails);
        }
      } else {
        // Fallback to mock data if no reports found
        setSummaryData(mockSummaryData);
        setTransactionDetails(mockTransactionDetails);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error loading transaction summary:', error);
      showNotification('Failed to load transaction summary', 'error');
      // Fallback to mock data on error
      setSummaryData(mockSummaryData);
      setTransactionDetails(mockTransactionDetails);
      setLoading(false);
    }
  };

  const handleSearch = () => {
    loadSummaryData();
    showNotification('Searching transaction summary...', 'info');
  };

  const handleCancel = () => {
    setSelectedDate(new Date().toISOString().split('T')[0]);
    setRunningNumber('2507013');
    showNotification('Form reset to default values', 'info');
  };

  const handleApprove = async () => {
    if (!summaryData || !user) {
      showNotification('No summary data to approve or user not authenticated', 'error');
      return;
    }

    try {
      // Get the current report ID from the latest report
      const result = await safeIpcInvoke('get-transaction-summary-reports', {
        startDate: selectedDate,
        endDate: selectedDate,
        status: 'GENERATED',
        page: 1,
        pageSize: 1
      });

      if (result.success && result.data.length > 0) {
        const reportId = result.data[0].id;

        const approveResult = await safeIpcInvoke('approve-transaction-summary-report', reportId, user.user_name);

        if (approveResult.success) {
          showNotification(`Transaction summary approved for ${formatCurrency(summaryData.totalAmount)} THB`, 'success');
          // Reload data to reflect the approval
          await loadSummaryData();
        } else {
          showNotification(`Failed to approve summary: ${approveResult.error}`, 'error');
        }
      } else {
        showNotification('No report found to approve', 'error');
      }
    } catch (error) {
      console.error('Error approving transaction summary:', error);
      showNotification('Failed to approve transaction summary', 'error');
    }
  };

  const handleClose = () => {
    // Navigate back or close the screen
    window.history.back();
  };

  const handlePrint = () => {
    window.print();
    showNotification('Print dialog opened', 'info');
  };

  const handleExport = () => {
    if (activeTab === 'summary' && summaryData) {
      // Export summary data as CSV
      const csvData = summaryData.items.map(item => ({
        Bank: item.bank,
        Group: item.group,
        MerchantID: item.merchantId,
        AccountNo: item.accountNo,
        AccountName: item.accountName,
        Amount: item.amount,
        Fax: item.fax,
        Email: item.email
      }));

      const csvContent = [
        Object.keys(csvData[0]).join(','),
        ...csvData.map(row => Object.values(row).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transaction-summary-${selectedDate}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      showNotification('Summary data exported successfully', 'success');
    } else if (activeTab === 'transaction' && transactionDetails.length > 0) {
      // Export transaction details as CSV
      const csvData = transactionDetails.map(item => ({
        TrnDate: item.trnDate,
        ServiceAC: item.serviceAC,
        MID: item.mid,
        Name: item.name,
        TID: item.tid,
        TrnAmt: item.trnAmt,
        SumMDR: item.sumMDR,
        VAT: item.vat,
        NetAmt: item.netAmt,
        WithholdTax: item.withholdTax,
        ReimbursementFee: item.reimbursementFee,
        ServiceFee: item.serviceFee,
        NetAmtCUP: item.netAmtCUP,
        BusinessTax: item.businessTax,
        TIDNo: item.tidNo
      }));

      const csvContent = [
        Object.keys(csvData[0]).join(','),
        ...csvData.map(row => Object.values(row).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transaction-details-${selectedDate}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      showNotification('Transaction details exported successfully', 'success');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('th-TH', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('th-TH', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // if (!hasReadAccess) {
  //   return (
  //     <div className="flex items-center justify-center h-64">
  //       <div className="text-center">
  //         <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
  //         <p className="text-gray-600">You don't have permission to view transaction summaries.</p>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="flex flex-col h-full bg-gray-50 font-sans">
      {/* Header */}
      <div className=" text-white shadow-sm border-b border-gray-200 p-3">
        <div className="flex items-center justify-between">
          {/* <h1 className="text-sm font-medium">
            STSOFT SOLUTION (TECHNICALSOFT CO.,LTD.) - ระบบ E-Pos ของร้าน ธุรกิจ - [PART DELIVERY SUMMARY]
          </h1> */}
          <div className="flex items-center gap-2">
            <Button variant="primary" size="sm" onClick={handleSearch} disabled={loading}>
              🔍 Search
            </Button>
            <Button variant="secondary" size="sm" onClick={handleCancel}>
              ❌ Cancel
            </Button>
            <RoleBasedComponent
              requiredPermission="canCreate"
              fallback={
                <Button variant="secondary" size="sm" disabled>
                  ✅ Approve (No Permission)
                </Button>
              }
            >
              <Button variant="primary" size="sm" onClick={handleApprove} disabled={!summaryData}>
                ✅ Approve
              </Button>
            </RoleBasedComponent>
            <Button variant="secondary" size="sm" onClick={handleClose}>
              ❌ Close
            </Button>
            <Button variant="secondary" size="sm" onClick={handlePrint}>
              🖨️ Print
            </Button>
            <Button variant="secondary" size="sm" onClick={handleExport}>
              📊 Export
            </Button>
          </div>
        </div>
      </div>

      {/* Control Panel */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <label className="flex items-center gap-1">
              <input
                type="radio"
                name="printType"
                value="transfer"
                checked={!showTransferDetails}
                onChange={() => setShowTransferDetails(false)}
                className="text-blue-600"
              />
              <span className="text-sm">Transfer</span>
            </label>
            <label className="flex items-center gap-1">
              <input
                type="radio"
                name="printType"
                value="pending"
                checked={showTransferDetails}
                onChange={() => setShowTransferDetails(true)}
                className="text-blue-600"
              />
              <span className="text-sm">Pending</span>
            </label>
          </div>
          
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">Running Number:</label>
            <input
              type="text"
              value={runningNumber}
              onChange={(e) => setRunningNumber(e.target.value)}
              className="px-2 py-1 border border-gray-300 rounded text-sm w-24"
            />
          </div>

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">Date:</label>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="px-2 py-1 border border-gray-300 rounded text-sm"
            />
          </div>

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">Trn No:</label>
            <input
              type="text"
              value={runningNumber}
              readOnly
              className="px-2 py-1 border border-gray-300 rounded text-sm w-24 bg-gray-50"
            />
          </div>
        </div>
      </div>

      {/* Summary Section */}
      {summaryData && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 p-6">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Transfer Today Card */}
              <div className="bg-white rounded-lg shadow-md border border-blue-200 p-6 hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-center justify-between mb-3">
                  <div className="text-sm font-medium text-blue-600 uppercase tracking-wide">
                    Transfer Today
                  </div>
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 text-lg">📈</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {formatCurrency(summaryData.transferToday)}
                </div>
                <div className="text-xs text-gray-500">
                  THB • Today's transfers
                </div>
              </div>

              {/* Pending Transfer Card */}
              <div className="bg-white rounded-lg shadow-md border border-orange-200 p-6 hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-center justify-between mb-3">
                  <div className="text-sm font-medium text-orange-600 uppercase tracking-wide">
                    Pending Transfer
                  </div>
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <span className="text-orange-600 text-lg">⏳</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-red-600 mb-1">
                  {formatCurrency(summaryData.pendingTransfer)}
                </div>
                <div className="text-xs text-gray-500">
                  THB • Awaiting processing
                </div>
              </div>

              {/* Total Amount Card */}
              <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg shadow-lg border border-gray-700 p-6 hover:shadow-xl transition-shadow duration-200">
                <div className="flex items-center justify-between mb-3">
                  <div className="text-sm font-medium text-gray-300 uppercase tracking-wide">
                    ยอดรวมทั้งสิ้น
                  </div>
                  <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-lg">💰</span>
                  </div>
                </div>
                <div className="text-3xl font-bold text-white mb-1">
                  {formatCurrency(summaryData.totalAmount)}
                </div>
                <div className="text-xs text-gray-400">
                  THB • Grand Total
                </div>
                <div className="mt-3 pt-3 border-t border-gray-700">
                  <div className="flex items-center text-xs text-green-400">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                    All transactions included
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Summary Stats */}
            <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div className="border-r border-gray-200 last:border-r-0">
                  <div className="text-lg font-semibold text-gray-900">
                    {summaryData.items.length}
                  </div>
                  <div className="text-xs text-gray-500 uppercase tracking-wide">
                    Total Records
                  </div>
                </div>
                <div className="border-r border-gray-200 last:border-r-0">
                  <div className="text-lg font-semibold text-green-600">
                    {summaryData.items.filter(item => item.amount > 0).length}
                  </div>
                  <div className="text-xs text-gray-500 uppercase tracking-wide">
                    Active Accounts
                  </div>
                </div>
                <div className="border-r border-gray-200 last:border-r-0">
                  <div className="text-lg font-semibold text-blue-600">
                    {formatCurrency(summaryData.totalAmount / summaryData.items.length)}
                  </div>
                  <div className="text-xs text-gray-500 uppercase tracking-wide">
                    Average Amount
                  </div>
                </div>
                <div className="border-r border-gray-200 last:border-r-0">
                  <div className="text-lg font-semibold text-purple-600">
                    {new Set(summaryData.items.map(item => item.group)).size}
                  </div>
                  <div className="text-xs text-gray-500 uppercase tracking-wide">
                    Unique Groups
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="flex">
          <button
            onClick={() => setActiveTab('summary')}
            className={`px-6 py-3 text-sm font-medium border-b-2 ${
              activeTab === 'summary'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Summary
          </button>
          <button
            onClick={() => {
              setActiveTab('transfer-today');
              loadTransferTodayData();
            }}
            className={`px-6 py-3 text-sm font-medium border-b-2 ${
              activeTab === 'transfer-today'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Transfer Today
          </button>
          <button
            onClick={() => setActiveTab('transaction')}
            className={`px-6 py-3 text-sm font-medium border-b-2 ${
              activeTab === 'transaction'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Transaction
          </button>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-auto bg-white">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-600">Loading transaction summary...</div>
          </div>
        ) : activeTab === 'summary' && summaryData ? (
          <div className="overflow-x-auto">
            <table className="w-full text-sm border-collapse">
              <thead className="bg-gray-100 border-b border-gray-200">
                <tr>
                  <th className="px-4 py-2 text-left font-medium text-gray-700 border-r border-gray-200 min-w-[200px]">Bank</th>
                  <th className="px-4 py-2 text-left font-medium text-gray-700 border-r border-gray-200">Group</th>
                  <th className="px-4 py-2 text-left font-medium text-gray-700 border-r border-gray-200">MerchantID</th>
                  <th className="px-4 py-2 text-left font-medium text-gray-700 border-r border-gray-200">Account No</th>
                  <th className="px-4 py-2 text-left font-medium text-gray-700 border-r border-gray-200 min-w-[150px]">Account Name</th>
                  <th className="px-4 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Amount</th>
                  <th className="px-4 py-2 text-center font-medium text-gray-700 border-r border-gray-200">Transfer Status</th>
                  <th className="px-4 py-2 text-left font-medium text-gray-700 border-r border-gray-200">Fax</th>
                  <th className="px-4 py-2 text-left font-medium text-gray-700">Email</th>
                </tr>
              </thead>
            <tbody>
              {summaryData.items.map((item, index) => (
                <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="px-4 py-2 border-r border-gray-200 text-xs">{item.bank}</td>
                  <td className="px-4 py-2 border-r border-gray-200 text-center">{item.group}</td>
                  <td className="px-4 py-2 border-r border-gray-200">{item.merchantId}</td>
                  <td className="px-4 py-2 border-r border-gray-200">{item.accountNo}</td>
                  <td className="px-4 py-2 border-r border-gray-200">{item.accountName}</td>
                  <td className="px-4 py-2 border-r border-gray-200 text-right font-mono">
                    {formatCurrency(item.amount)}
                  </td>
                  <td className="px-4 py-2 border-r border-gray-200 text-center">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      item.isTransfer === 1
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {item.isTransfer === 1 ? '✅ Transferred' : '⏳ Pending'}
                    </span>
                  </td>
                  <td className="px-4 py-2 border-r border-gray-200">{item.fax}</td>
                  <td className="px-4 py-2">{item.email}</td>
                </tr>
              ))}
            </tbody>
            </table>
          </div>
        ) : activeTab === 'transfer-today' ? (
          transferTodayLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-600">Loading transfer today data...</div>
            </div>
          ) : transferTodayData.length > 0 ? (
            <div className="overflow-x-auto">
              <div className="mb-4 p-4 bg-blue-50 border-l-4 border-blue-400">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-blue-700">
                        <strong>Pending Transfers:</strong> {transferTodayData.length} records with total amount of{' '}
                        <strong>{formatCurrency(transferTodayData.reduce((sum, item) => sum + parseFloat(item.final_net_amount.toString()), 0))} THB</strong>
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <RoleBasedComponent requiredPermission="canUpdate">
                      <Button
                        onClick={() => markTransfersAsCompleted(transferTodayData.map(item => item.id))}
                        className="bg-green-600 hover:bg-green-700 text-white text-sm px-4 py-2"
                        disabled={transferTodayData.length === 0}
                      >
                        Mark All as Transferred
                      </Button>
                    </RoleBasedComponent>
                    <Button
                      onClick={loadTransferTodayData}
                      className="bg-blue-600 hover:bg-blue-700 text-white text-sm px-4 py-2"
                    >
                      Refresh
                    </Button>
                  </div>
                </div>
              </div>
              <table className="w-full text-sm border-collapse">
                <thead className="bg-gray-100 border-b border-gray-200">
                  <tr>
                    <th className="px-4 py-2 text-left font-medium text-gray-700 border-r border-gray-200">Merchant VAT</th>
                    <th className="px-4 py-2 text-left font-medium text-gray-700 border-r border-gray-200">Merchant Name</th>
                    <th className="px-4 py-2 text-left font-medium text-gray-700 border-r border-gray-200">Transaction Date</th>
                    <th className="px-4 py-2 text-left font-medium text-gray-700 border-r border-gray-200">Channel</th>
                    <th className="px-4 py-2 text-center font-medium text-gray-700 border-r border-gray-200">Count</th>
                    <th className="px-4 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Total Amount</th>
                    <th className="px-4 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Transfer Fee</th>
                    <th className="px-4 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Final Net Amount</th>
                    <th className="px-4 py-2 text-center font-medium text-gray-700 border-r border-gray-200">Status</th>
                    <th className="px-4 py-2 text-left font-medium text-gray-700">Created</th>
                  </tr>
                </thead>
                <tbody>
                  {transferTodayData.map((item, index) => (
                    <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-4 py-2 border-r border-gray-200 font-mono text-xs">{item.merchant_vat}</td>
                      <td className="px-4 py-2 border-r border-gray-200">{item.merchant_name}</td>
                      <td className="px-4 py-2 border-r border-gray-200">{new Date(item.transaction_date).toLocaleDateString()}</td>
                      <td className="px-4 py-2 border-r border-gray-200">{item.channel_type}</td>
                      <td className="px-4 py-2 border-r border-gray-200 text-center">{item.transaction_count}</td>
                      <td className="px-4 py-2 border-r border-gray-200 text-right font-mono">
                        {formatCurrency(parseFloat(item.total_amount.toString()))}
                      </td>
                      <td className="px-4 py-2 border-r border-gray-200 text-right font-mono">
                        {formatCurrency(parseFloat(item.transfer_fee.toString()))}
                      </td>
                      <td className="px-4 py-2 border-r border-gray-200 text-right font-mono font-semibold">
                        {formatCurrency(parseFloat(item.final_net_amount.toString()))}
                      </td>
                      <td className="px-4 py-2 border-r border-gray-200 text-center">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          ⏳ Pending
                        </span>
                      </td>
                      <td className="px-4 py-2 text-xs text-gray-500">
                        {new Date(item.create_dt).toLocaleDateString()} {new Date(item.create_dt).toLocaleTimeString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No pending transfers</h3>
                <p className="mt-1 text-sm text-gray-500">All transfers have been completed.</p>
              </div>
            </div>
          )
        ) : activeTab === 'transaction' && transactionDetails.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full text-xs border-collapse min-w-[1200px]">
            <thead className="bg-gray-100 border-b border-gray-200">
              <tr>
                <th className="px-2 py-2 text-left font-medium text-gray-700 border-r border-gray-200">Trn Date</th>
                <th className="px-2 py-2 text-left font-medium text-gray-700 border-r border-gray-200">Service A/C</th>
                <th className="px-2 py-2 text-left font-medium text-gray-700 border-r border-gray-200">MID</th>
                <th className="px-2 py-2 text-left font-medium text-gray-700 border-r border-gray-200">Name</th>
                <th className="px-2 py-2 text-left font-medium text-gray-700 border-r border-gray-200">TID</th>
                <th className="px-2 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Trn Amt</th>
                <th className="px-2 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Sum MDR</th>
                <th className="px-2 py-2 text-right font-medium text-gray-700 border-r border-gray-200">VAT</th>
                <th className="px-2 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Net Amt</th>
                <th className="px-2 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Withhold TAX</th>
                <th className="px-2 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Reimbursement Fee</th>
                <th className="px-2 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Service Fee</th>
                <th className="px-2 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Net Amt CUP</th>
                <th className="px-2 py-2 text-right font-medium text-gray-700 border-r border-gray-200">Business TAX</th>
                <th className="px-2 py-2 text-right font-medium text-gray-700">TID No</th>
              </tr>
            </thead>
            <tbody>
              {transactionDetails.map((item, index) => (
                <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="px-2 py-2 border-r border-gray-200">{item.trnDate}</td>
                  <td className="px-2 py-2 border-r border-gray-200">{item.serviceAC}</td>
                  <td className="px-2 py-2 border-r border-gray-200">{item.mid}</td>
                  <td className="px-2 py-2 border-r border-gray-200">{item.name}</td>
                  <td className="px-2 py-2 border-r border-gray-200 text-center">{item.tid}</td>
                  <td className="px-2 py-2 border-r border-gray-200 text-right font-mono">
                    {formatCurrency(item.trnAmt)}
                  </td>
                  <td className="px-2 py-2 border-r border-gray-200 text-right font-mono">
                    {formatCurrency(item.sumMDR)}
                  </td>
                  <td className="px-2 py-2 border-r border-gray-200 text-right font-mono">
                    {formatCurrency(item.vat)}
                  </td>
                  <td className="px-2 py-2 border-r border-gray-200 text-right font-mono">
                    {formatCurrency(item.netAmt)}
                  </td>
                  <td className="px-2 py-2 border-r border-gray-200 text-right font-mono">
                    {formatCurrency(item.withholdTax)}
                  </td>
                  <td className="px-2 py-2 border-r border-gray-200 text-right font-mono">
                    {formatCurrency(item.reimbursementFee)}
                  </td>
                  <td className="px-2 py-2 border-r border-gray-200 text-right font-mono">
                    {formatCurrency(item.serviceFee)}
                  </td>
                  <td className="px-2 py-2 border-r border-gray-200 text-right font-mono">
                    {formatCurrency(item.netAmtCUP)}
                  </td>
                  <td className="px-2 py-2 border-r border-gray-200 text-right font-mono">
                    {formatCurrency(item.businessTax)}
                  </td>
                  <td className="px-2 py-2 text-center">{item.tidNo}</td>
                </tr>
              ))}
            </tbody>
            </table>
          </div>
        ) : (
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-600">
              {activeTab === 'summary'
                ? 'No transaction summary data available'
                : (activeTab as string) === 'transfer-today'
                ? 'No pending transfers found'
                : 'No transaction details available'}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
