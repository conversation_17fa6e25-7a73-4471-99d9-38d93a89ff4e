import React, { useState, useEffect, useCallback } from "react";
import { Button } from "../button";
import type { Merchant, MerchantUnionpay } from "../../types/merchant";

interface MerchantUnionpayTabProps {
  merchantUnionpay: MerchantUnionpay | null;
  onSave: (unionpayData: Omit<MerchantUnionpay, "merchant_unionpay_id" | "create_dt" | "update_dt">) => Promise<void>;
  editingMerchant: Merchant | null;
  readOnly?: boolean;
}

export function MerchantUnionpayTab({
  merchantUnionpay,
  onSave,
  editingMerchant,
  readOnly = false
}: MerchantUnionpayTabProps) {
  const [formData, setFormData] = useState({
    mdr_local: '',
    mdr_normal: '',
    mdr_premium: '',
    mdr_diamond: '',
    mdr_qrcode: '',
    active: true,
  });
  const [isSaving, setIsSaving] = useState(false);

  // Memoized input change handler to prevent focus loss
  const handleInputChange = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  // Update form data when merchantUnionpay changes
  useEffect(() => {
    if (merchantUnionpay) {
      setFormData({
        mdr_local: (merchantUnionpay.mdr_local || '').toString(),
        mdr_normal: (merchantUnionpay.mdr_normal || '').toString(),
        mdr_premium: (merchantUnionpay.mdr_premium || '').toString(),
        mdr_diamond: (merchantUnionpay.mdr_diamond || '').toString(),
        mdr_qrcode: (merchantUnionpay.mdr_qrcode || '').toString(),
        active: merchantUnionpay.active,
      });
    } else {
      setFormData({
        mdr_local: '',
        mdr_normal: '',
        mdr_premium: '',
        mdr_diamond: '',
        mdr_qrcode: '',
        active: true,
      });
    }
  }, [merchantUnionpay]);

  const handleSave = async () => {
    if (!editingMerchant?.merchant_id) {
      alert("Please save merchant details first");
      return;
    }

    setIsSaving(true);
    try {
      await onSave({
        merchant_id: editingMerchant.merchant_id,
        mdr_local: formData.mdr_local ? parseFloat(formData.mdr_local) : undefined,
        mdr_normal: formData.mdr_normal ? parseFloat(formData.mdr_normal) : undefined,
        mdr_premium: formData.mdr_premium ? parseFloat(formData.mdr_premium) : undefined,
        mdr_diamond: formData.mdr_diamond ? parseFloat(formData.mdr_diamond) : undefined,
        mdr_qrcode: formData.mdr_qrcode ? parseFloat(formData.mdr_qrcode) : undefined,
        active: formData.active,
        create_by: "SYSTEM",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (!editingMerchant?.merchant_id) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500 mb-4">
          <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-lg font-medium">Save Merchant Details First</p>
          <p className="text-sm">Please save the merchant details before configuring UnionPay settings.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">UnionPay Settings</h3>
        <p className="text-sm text-gray-600 mb-6">
          Configure UnionPay MDR (Merchant Discount Rate) settings for different card types.
        </p>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="space-y-6">
          {/* MDR Rates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Local Card Rate */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Local Card MDR (%)
              </label>
              <div className="relative">
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  value={formData.mdr_local}
                  onChange={(e) => handleInputChange("mdr_local", e.target.value ? parseFloat(e.target.value) : '')}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12 ${
                    readOnly ? "bg-gray-50 cursor-not-allowed" : ""
                  }`}
                  placeholder="0.00"
                  readOnly={readOnly}
                  disabled={readOnly}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span className="text-gray-500 text-sm">%</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Rate for local UnionPay cards
              </p>
            </div>

            {/* Normal Card Rate */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Normal Card MDR (%)
              </label>
              <div className="relative">
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  value={formData.mdr_normal}
                  onChange={(e) => handleInputChange("mdr_normal", e.target.value ? parseFloat(e.target.value) : '')}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12 ${
                    readOnly ? "bg-gray-50 cursor-not-allowed" : ""
                  }`}
                  placeholder="0.00"
                  readOnly={readOnly}
                  disabled={readOnly}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span className="text-gray-500 text-sm">%</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Rate for standard UnionPay cards
              </p>
            </div>

            {/* Premium Card Rate */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Premium Card MDR (%)
              </label>
              <div className="relative">
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  value={formData.mdr_premium}
                  onChange={(e) => handleInputChange("mdr_premium", e.target.value ? parseFloat(e.target.value) : '')}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12 ${
                    readOnly ? "bg-gray-50 cursor-not-allowed" : ""
                  }`}
                  placeholder="0.00"
                  readOnly={readOnly}
                  disabled={readOnly}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span className="text-gray-500 text-sm">%</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Rate for premium UnionPay cards
              </p>
            </div>

            {/* Diamond Card Rate */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Diamond Card MDR (%)
              </label>
              <div className="relative">
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  value={formData.mdr_diamond}
                  onChange={(e) => handleInputChange("mdr_diamond", e.target.value ? parseFloat(e.target.value) : '')}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12 ${
                    readOnly ? "bg-gray-50 cursor-not-allowed" : ""
                  }`}
                  placeholder="0.00"
                  readOnly={readOnly}
                  disabled={readOnly}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span className="text-gray-500 text-sm">%</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Rate for diamond/elite UnionPay cards
              </p>
            </div>

            {/* QR Code Rate */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                QR Code MDR (%)
              </label>
              <div className="relative">
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  value={formData.mdr_qrcode}
                  onChange={(e) => handleInputChange("mdr_qrcode", e.target.value ? parseFloat(e.target.value) : '')}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12 ${
                    readOnly ? "bg-gray-50 cursor-not-allowed" : ""
                  }`}
                  placeholder="0.00"
                  readOnly={readOnly}
                  disabled={readOnly}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span className="text-gray-500 text-sm">%</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Rate for QR code payments
              </p>
            </div>
          </div>

          {/* Active Status */}
          <div className="pt-4 border-t border-gray-200">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="unionpay-active"
                checked={formData.active}
                onChange={(e) => handleInputChange("active", e.target.checked)}
                className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                  readOnly ? "cursor-not-allowed" : ""
                }`}
                disabled={readOnly}
              />
              <label htmlFor="unionpay-active" className="ml-2 block text-sm text-gray-900">
                Enable UnionPay for this merchant
              </label>
            </div>
            <p className="text-xs text-gray-500 mt-1 ml-6">
              When enabled, this merchant can accept UnionPay card payments
            </p>
          </div>

          {/* Current Settings Display */}
          {merchantUnionpay && (
            <div className="bg-gray-50 p-4 rounded-lg mt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Current Settings</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Local Card MDR:</span>
                  <span className="font-medium">{merchantUnionpay.mdr_local || 0}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Normal Card MDR:</span>
                  <span className="font-medium">{merchantUnionpay.mdr_normal || 0}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Premium Card MDR:</span>
                  <span className="font-medium">{merchantUnionpay.mdr_premium || 0}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Diamond Card MDR:</span>
                  <span className="font-medium">{merchantUnionpay.mdr_diamond || 0}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">QR Code MDR:</span>
                  <span className="font-medium">{merchantUnionpay.mdr_qrcode || 0}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className={`font-medium ${merchantUnionpay.active ? 'text-green-600' : 'text-red-600'}`}>
                    {merchantUnionpay.active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Save Button - Hidden in read-only mode */}
          {!readOnly && (
            <div className="flex justify-end pt-4 border-t border-gray-200">
              <Button
                onClick={handleSave}
                variant="primary"
                disabled={isSaving}
                className="min-w-[120px]"
              >
                {isSaving ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </div>
                ) : (
                  merchantUnionpay ? "Update Settings" : "Save Settings"
                )}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Information Panel */}
      {/* <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              UnionPay MDR Information
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>MDR (Merchant Discount Rate) is the percentage fee charged for each transaction</li>
                <li>Different card types have different MDR rates</li>
                <li>All rates should be between 0% and 100%</li>
                <li>Disabling UnionPay will prevent this merchant from accepting UnionPay card payments</li>
                <li>Changes take effect immediately after saving</li>
              </ul>
            </div>
          </div>
        </div>
      </div> */}
    </div>
  );
}
