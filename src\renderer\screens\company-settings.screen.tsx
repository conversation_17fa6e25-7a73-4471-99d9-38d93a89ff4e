import React, { useState, useEffect } from "react";
import { Button } from "../components/button";
import { safeIpcInvoke } from "../utils/electron";
import { useNotification } from "../contexts/NotificationContext";
import { useMasterDataAccess } from "../hooks/useRoleAccess";
import { RoleBasedComponent } from "../components/RoleBasedComponent";

interface CompanySettings {
  id?: number;
  // Company Information
  company_name_th: string;
  company_name_en: string;
  contact_name: string;
  tax_id: string;
  iin: string;
  
  // Address Information
  address_th: string;
  address_en: string;
  
  // Contact Information
  telephone: string;
  fax: string;
  email: string;
  website: string;
  
  // Additional Information
  additional_info: string;
  
  // Banking Information
  bank_name: string;
  branch: string;
  rate: string;
  
  // Company Logo
  logo_path?: string;
  
  // Status
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

export function CompanySettingsScreen() {
  const [settings, setSettings] = useState<CompanySettings | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const { showNotification } = useNotification();
  const { canUpdate, userRole, isAdmin } = useMasterDataAccess();
  const [formData, setFormData] = useState<Omit<CompanySettings, "id" | "create_dt" | "update_dt">>({
    company_name_th: "อีพอส เซอร์วิส จำกัด",
    company_name_en: "E-POS Service Company Limited",
    contact_name: "Urawee Sittichai",
    tax_id: "************",
    iin: "********",
    address_th: "8 อาคารศิริน อีกที่15,16,17,20,25,26,27 ซ.สุขุมวิท40 แขวงพระโขนง เขตคลองเตย กรุงเทพมหานคร 10110",
    address_en: "8 T.One Building 15-17, 20, 25-27Fl. Soi Sukhumvit 40 Phra Khanong, Khlongtoei, Bangkok 10110",
    telephone: "(662) ********",
    fax: "",
    email: "<EMAIL>",
    website: "www.eposservice.com",
    additional_info: "",
    bank_name: "",
    branch: "",
    rate: "0.00",
    logo_path: "",
    active: true,
    create_by: "SYSTEM",
  });

  // Load company settings from database
  const loadCompanySettings = async () => {
    try {
      setIsInitializing(true);

      // Initialize table first
      await safeIpcInvoke('init-company-setting-table');

      // Load existing settings
      const response = await safeIpcInvoke('get-company-settings');

      if (response.success && response.data) {
        setSettings(response.data);
        setFormData({
          company_name_th: response.data.company_name_th,
          company_name_en: response.data.company_name_en,
          contact_name: response.data.contact_name,
          tax_id: response.data.tax_id || "",
          iin: response.data.iin || "",
          address_th: response.data.address_th || "",
          address_en: response.data.address_en || "",
          telephone: response.data.telephone || "",
          fax: response.data.fax || "",
          email: response.data.email || "",
          website: response.data.website || "",
          additional_info: response.data.additional_info || "",
          bank_name: response.data.bank_name || "",
          branch: response.data.branch || "",
          rate: response.data.rate || "0.00",
          logo_path: response.data.logo_path || "",
          active: response.data.active,
          create_by: response.data.create_by,
        });
      } else {
        // No settings found, use default values
        const defaultSettings: CompanySettings = {
          company_name_th: "อีพอส เซอร์วิส จำกัด",
          company_name_en: "E-POS Service Company Limited",
          contact_name: "Urawee Sittichai",
          tax_id: "************",
          iin: "********",
          address_th: "8 อาคารศิริน อีกที่15,16,17,20,25,26,27 ซ.สุขุมวิท40 แขวงพระโขนง เขตคลองเตย กรุงเทพมหานคร 10110",
          address_en: "8 T.One Building 15-17, 20, 25-27Fl. Soi Sukhumvit 40 Phra Khanong, Khlongtoei, Bangkok 10110",
          telephone: "(662) ********",
          fax: "",
          email: "<EMAIL>",
          website: "www.eposservice.com",
          additional_info: "",
          bank_name: "",
          branch: "",
          rate: "0.00",
          logo_path: "",
          active: true,
          create_by: "SYSTEM",
        };

        setFormData(defaultSettings);
      }
    } catch (error: any) {
      console.error('Error loading company settings:', error);
      showNotification('Error loading company settings: ' + error.message, 'error');
    } finally {
      setIsInitializing(false);
    }
  };

  useEffect(() => {
    loadCompanySettings();
  }, []);

  const handleEdit = () => {
    if (!canUpdate || !isAdmin) {
      showNotification("You don't have permission to edit company settings. Admin access required.", "error");
      return;
    }
    setIsEditing(true);
  };

  const handleCancel = () => {
    if (settings) {
      setFormData({
        company_name_th: settings.company_name_th,
        company_name_en: settings.company_name_en,
        contact_name: settings.contact_name,
        tax_id: settings.tax_id,
        iin: settings.iin,
        address_th: settings.address_th,
        address_en: settings.address_en,
        telephone: settings.telephone,
        fax: settings.fax,
        email: settings.email,
        website: settings.website,
        additional_info: settings.additional_info,
        bank_name: settings.bank_name,
        branch: settings.branch,
        rate: settings.rate,
        logo_path: settings.logo_path || "",
        active: settings.active,
        create_by: settings.create_by,
      });
    }
    setIsEditing(false);
  };

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!canUpdate || !isAdmin) {
      showNotification("You don't have permission to save company settings. Admin access required.", "error");
      return;
    }

    setIsLoading(true);

    try {
      // Save to database
      const response = await safeIpcInvoke('save-company-settings', {
        ...formData,
        update_by: "SYSTEM"
      });

      if (response.success && response.data) {
        setSettings(response.data);
        setIsEditing(false);
        showNotification('Company settings saved successfully', 'success');
      } else {
        throw new Error(response.error || 'Failed to save company settings');
      }
    } catch (error: any) {
      console.error("Error saving settings:", error);
      showNotification('Error saving settings: ' + error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogoSelect = () => {
    // Mock logo selection
    alert("Logo selection functionality would be implemented here");
  };

  // Helper function to get field value safely
  const getFieldValue = (field: keyof CompanySettings): string => {
    if (isEditing) {
      const value = formData[field as keyof typeof formData];
      return String(value || "");
    }
    const value = settings?.[field] || formData[field as keyof typeof formData];
    return String(value || "");
  };

  if (isInitializing) {
    return (
      <div className="min-h-screen bg-gray-50 p-4 sm:p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading company settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Company Settings</h1>
              <p className="text-gray-600 mt-1">
                Manage company information and system settings
              </p>
              {/* <p className="text-sm text-blue-600 mt-1">
                Role: {userRole?.toUpperCase()} | Access: {isAdmin ? 'Admin (Full Access)' : 'Limited Access'}
              </p> */}
            </div>
            <div className="flex gap-2">
              {!isEditing ? (
                <RoleBasedComponent
                  requiredPermission="isAdmin"
                  fallback={
                    <span className="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium bg-gray-300 text-gray-500 cursor-not-allowed">
                      Edit Disabled (Admin Only)
                    </span>
                  }
                >
                  <Button
                    onClick={handleEdit}
                    variant="primary"
                    size="md"
                    className="inline-flex items-center gap-2"
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                     Edit
                  </Button>
                </RoleBasedComponent>
              ) : (
                <>
                  <Button
                    onClick={handleCancel}
                    variant="secondary"
                    size="md"
                    disabled={isLoading}
                  >
                     Cancel
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    variant="primary"
                    size="md"
                    disabled={isLoading}
                    className="inline-flex items-center gap-2"
                  >
                    {isLoading ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    )}
                     Save Changes
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Company Settings Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column - Company Information */}
                <div className="lg:col-span-3 space-y-6">
                  {/* Company Names */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Company Name (Thai)
                      </label>
                      <input
                        type="text"
                        value={isEditing ? formData.company_name_th : (settings?.company_name_th || formData.company_name_th)}
                        onChange={(e) => handleInputChange("company_name_th", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                        style={{ fontFamily: 'Noto Sans Thai, sans-serif' }}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Company Name (English)
                      </label>
                      <input
                        type="text"
                        value={isEditing ? formData.company_name_en : (settings?.company_name_en || formData.company_name_en)}
                        onChange={(e) => handleInputChange("company_name_en", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                    </div>
                  </div>

                  {/* Contact and IDs */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Contact Name
                      </label>
                      <input
                        type="text"
                        value={getFieldValue("contact_name")}
                        onChange={(e) => handleInputChange("contact_name", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        TAX I.D.
                      </label>
                      <input
                        type="text"
                        value={getFieldValue("tax_id")}
                        onChange={(e) => handleInputChange("tax_id", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        IIN
                      </label>
                      <input
                        type="text"
                        value={getFieldValue("iin")}
                        onChange={(e) => handleInputChange("iin", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Address Section */}
              <div className="space-y-4 mt-3">
                <h3 className="text-lg font-semibold text-gray-900">Address</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Address (Thai)
                  </label>
                  <textarea
                    rows={3}
                    value={getFieldValue("address_th")}
                    onChange={(e) => handleInputChange("address_th", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                    style={{ fontFamily: 'Noto Sans Thai, sans-serif' }}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Address (English)
                  </label>
                  <textarea
                    rows={3}
                    value={getFieldValue("address_en")}
                    onChange={(e) => handleInputChange("address_en", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Telephone
                    </label>
                    <input
                      type="text"
                      value={getFieldValue("telephone")}
                      onChange={(e) => handleInputChange("telephone", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Fax
                    </label>
                    <input
                      type="text"
                      value={getFieldValue("fax")}
                      onChange={(e) => handleInputChange("fax", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      value={getFieldValue("email")}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Website
                    </label>
                    <input
                      type="url"
                      value={getFieldValue("website")}
                      onChange={(e) => handleInputChange("website", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4 mt-2">
                <h3 className="text-lg font-semibold text-gray-900">Additional Information</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Details
                  </label>
                  <textarea
                    rows={3}
                    value={getFieldValue("additional_info")}
                    onChange={(e) => handleInputChange("additional_info", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                    style={{ fontFamily: 'Noto Sans Thai, sans-serif' }}
                  />
                </div>
              </div>

              {/* Banking Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Banking Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bank Name
                    </label>
                    <input
                      type="text"
                      value={getFieldValue("bank_name")}
                      onChange={(e) => handleInputChange("bank_name", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Branch
                    </label>
                    <input
                      type="text"
                      value={getFieldValue("branch")}
                      onChange={(e) => handleInputChange("branch", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Rate
                    </label>
                    <input
                      type="text"
                      value={getFieldValue("rate")}
                      onChange={(e) => handleInputChange("rate", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
