import React, { useState, useEffect } from 'react';
import { PDFViewer, PDFDownloadLink, pdf } from '@react-pdf/renderer';
import { TransactionSummaryReport } from './TransactionSummaryReport';
import { ReportService, ReportSummary } from '../services/reportService';
import { Button } from './button';

interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  filters: any;
}

export const ReportModal: React.FC<ReportModalProps> = ({ isOpen, onClose, filters }) => {
  const [reportData, setReportData] = useState<ReportSummary | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [zoom, setZoom] = useState(1);

  useEffect(() => {
    if (isOpen) {
      generateReport();
    }
  }, [isOpen, filters]);

  const generateReport = async () => {
    setIsLoading(true);
    setError('');
    
    try {
      console.log('📊 Generating transaction summary report...');
      const data = await ReportService.generateTransactionSummaryReport(filters);
      setReportData(data);
      console.log('✅ Report generated successfully');
    } catch (err) {
      console.error('❌ Error generating report:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate report');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrint = async () => {
    if (!reportData) return;

    try {
      console.log('🖨️ Generating PDF for printing...');
      const blob = await pdf(<TransactionSummaryReport reportData={reportData} />).toBlob();
      const url = URL.createObjectURL(blob);
      const printWindow = window.open(url);
      if (printWindow) {
        printWindow.onload = () => {
          printWindow.print();
        };
      }
      console.log('✅ PDF print initiated successfully');
    } catch (error) {
      console.error('❌ Error printing report:', error);
      setError('Failed to print report. Please try again.');
    }
  };

  const handleDownload = async () => {
    if (!reportData) return;

    try {
      console.log('📥 Generating PDF for download...');
      const blob = await pdf(<TransactionSummaryReport reportData={reportData} />).toBlob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `transaction-summary-report-${reportData.reportDate.replace(/\//g, '-')}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      console.log('✅ PDF download initiated successfully');
    } catch (error) {
      console.error('❌ Error downloading report:', error);
      setError('Failed to download report. Please try again.');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-7xl h-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">📊 Transaction Summary Report</h2>
            <p className="text-sm text-gray-600 mt-1">
              {reportData ? `${reportData.merchants.length} merchants, ${reportData.grandTotals.totalTransactions} transactions` : 'Generating report...'}
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {/* Zoom Controls */}
            {reportData && (
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => setZoom(Math.max(0.5, zoom - 0.1))}
                  variant="secondary"
                  size="sm"
                  disabled={zoom <= 0.5}
                >
                  🔍-
                </Button>
                <span className="text-sm text-gray-600 min-w-[60px] text-center">
                  {Math.round(zoom * 100)}%
                </span>
                <Button
                  onClick={() => setZoom(Math.min(2, zoom + 0.1))}
                  variant="secondary"
                  size="sm"
                  disabled={zoom >= 2}
                >
                  🔍+
                </Button>
              </div>
            )}
            
            {/* Action Buttons */}
            {reportData && (
              <>
                <Button
                  onClick={handlePrint}
                  variant="secondary"
                  size="sm"
                >
                  🖨️ Print
                </Button>
                
                <Button
                  onClick={handleDownload}
                  variant="primary"
                  size="sm"
                >
                  📥 Download PDF
                </Button>
              </>
            )}
            
            <Button
              onClick={onClose}
              variant="secondary"
              size="sm"
            >
              ✕ Close
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {isLoading && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-600">Generating transaction summary report...</p>
                <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-red-500 text-6xl mb-4">⚠️</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Report Generation Failed</h3>
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={generateReport} variant="primary">
                  🔄 Retry
                </Button>
              </div>
            </div>
          )}

          {reportData && !isLoading && !error && (
            <div className="h-full overflow-auto p-4">
              <div style={{ transform: `scale(${zoom})`, transformOrigin: 'top left' }}>
                <PDFViewer
                  width="100%"
                  height="700px"
                  style={{
                    border: '2px solid #e5e7eb',
                    borderRadius: '12px',
                  }}
                >
                  <TransactionSummaryReport reportData={reportData} />
                </PDFViewer>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {reportData && (
          <div className="border-t border-gray-200 p-4 bg-gray-50">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div>
                <span className="font-medium">Report Summary:</span>
                <span className="ml-2">
                  {reportData.merchants.length} merchants • 
                  {reportData.grandTotals.totalTransactions} transactions • 
                  Total Amount: {ReportService.formatCurrency(reportData.grandTotals.totalAmount)}
                </span>
              </div>
              <div>
                Generated: {reportData.reportDate} {reportData.reportTime}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
