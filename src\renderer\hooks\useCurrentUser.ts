import { useAuth } from '../contexts/AuthContext';

/**
 * Hook to get current user information for create_by and update_by fields
 */
export function useCurrentUser() {
  const { user } = useAuth();
  
  const currentUserName = user?.user_name || 'SYSTEM';
  
  /**
   * Automatically inject create_by field for new records
   */
  const withCreateBy = <T extends Record<string, any>>(data: T): T & { create_by: string } => {
    return {
      ...data,
      create_by: currentUserName
    };
  };

  /**
   * Automatically inject update_by field for existing records
   */
  const withUpdateBy = <T extends Record<string, any>>(data: T): T & { update_by: string } => {
    return {
      ...data,
      update_by: currentUserName
    };
  };

  /**
   * Automatically inject both create_by and update_by fields
   */
  const withUserFields = <T extends Record<string, any>>(data: T): T & { create_by: string; update_by: string } => {
    return {
      ...data,
      create_by: currentUserName,
      update_by: currentUserName
    };
  };

  /**
   * Create form data with create_by field for new records
   */
  const createFormData = <T extends Record<string, any>>(initialData: T): T & { create_by: string } => {
    return withCreateBy(initialData);
  };

  /**
   * Update form data with update_by field for existing records
   */
  const updateFormData = <T extends Record<string, any>>(existingData: T): T & { update_by: string } => {
    return withUpdateBy(existingData);
  };

  /**
   * Get current user information
   */
  const getCurrentUser = () => ({
    user_name: currentUserName,
    user_id: user?.user_id,
    role_code: user?.role_code,
    role_name: user?.role_name
  });

  return {
    currentUserName,
    user,
    withCreateBy,
    withUpdateBy,
    withUserFields,
    createFormData,
    updateFormData,
    getCurrentUser
  };
}
