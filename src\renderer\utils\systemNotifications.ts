import { safeIpcInvoke } from './electron';

// Interface for notification options
interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  silent?: boolean;
  urgency?: 'normal' | 'critical' | 'low';
}

// Interface for dialog options
interface DialogOptions {
  type?: 'none' | 'info' | 'error' | 'question' | 'warning';
  title?: string;
  message: string;
  detail?: string;
  buttons?: string[];
  defaultId?: number;
  cancelId?: number;
}

/**
 * Show a system notification with custom icon
 */
export const showSystemNotification = async (options: NotificationOptions): Promise<boolean> => {
  try {
    const result = await safeIpcInvoke('show-system-notification', options);
    return result.success;
  } catch (error) {
    console.error('❌ Error showing system notification:', error);
    return false;
  }
};

/**
 * Show a custom dialog with custom icon
 */
export const showCustomDialog = async (options: DialogOptions): Promise<{ success: boolean; response?: number; checkboxChecked?: boolean }> => {
  try {
    const result = await safeIpcInvoke('show-custom-dialog', options);
    return result;
  } catch (error) {
    console.error('❌ Error showing custom dialog:', error);
    return { success: false };
  }
};

/**
 * Show a confirmation dialog with custom icon
 */
export const showConfirmationDialog = async (message: string, title?: string): Promise<boolean> => {
  try {
    const result = await safeIpcInvoke('show-confirmation-dialog', message, title);
    return result.success && result.confirmed;
  } catch (error) {
    console.error('❌ Error showing confirmation dialog:', error);
    return false;
  }
};

/**
 * Show an error dialog with custom icon
 */
export const showErrorDialog = async (message: string, title?: string, detail?: string): Promise<boolean> => {
  try {
    const result = await safeIpcInvoke('show-error-dialog', message, title, detail);
    return result.success;
  } catch (error) {
    console.error('❌ Error showing error dialog:', error);
    return false;
  }
};

/**
 * Show a warning dialog with custom icon
 */
export const showWarningDialog = async (message: string, title?: string, detail?: string): Promise<boolean> => {
  try {
    const result = await safeIpcInvoke('show-warning-dialog', message, title, detail);
    return result.success;
  } catch (error) {
    console.error('❌ Error showing warning dialog:', error);
    return false;
  }
};

/**
 * Show an info dialog with custom icon
 */
export const showInfoDialog = async (message: string, title?: string, detail?: string): Promise<boolean> => {
  try {
    const result = await safeIpcInvoke('show-info-dialog', message, title, detail);
    return result.success;
  } catch (error) {
    console.error('❌ Error showing info dialog:', error);
    return false;
  }
};

/**
 * Create system tray with custom icon
 */
export const createSystemTray = async (): Promise<boolean> => {
  try {
    const result = await safeIpcInvoke('create-system-tray');
    return result.success;
  } catch (error) {
    console.error('❌ Error creating system tray:', error);
    return false;
  }
};

/**
 * Destroy system tray
 */
export const destroySystemTray = async (): Promise<boolean> => {
  try {
    const result = await safeIpcInvoke('destroy-system-tray');
    return result.success;
  } catch (error) {
    console.error('❌ Error destroying system tray:', error);
    return false;
  }
};

/**
 * Update system tray tooltip
 */
export const updateTrayTooltip = async (tooltip: string): Promise<boolean> => {
  try {
    const result = await safeIpcInvoke('update-tray-tooltip', tooltip);
    return result.success;
  } catch (error) {
    console.error('❌ Error updating tray tooltip:', error);
    return false;
  }
};

/**
 * Show tray balloon notification (Windows only)
 */
export const showTrayBalloon = async (title: string, content: string): Promise<boolean> => {
  try {
    const result = await safeIpcInvoke('show-tray-balloon', { title, content });
    return result.success;
  } catch (error) {
    console.error('❌ Error showing tray balloon:', error);
    return false;
  }
};

// Convenience functions for common use cases

/**
 * Show success notification
 */
export const showSuccessNotification = (message: string, title: string = 'Success') => {
  return showSystemNotification({
    title,
    body: message,
    urgency: 'normal'
  });
};

/**
 * Show error notification
 */
export const showErrorNotification = (message: string, title: string = 'Error') => {
  return showSystemNotification({
    title,
    body: message,
    urgency: 'critical'
  });
};

/**
 * Show warning notification
 */
export const showWarningNotification = (message: string, title: string = 'Warning') => {
  return showSystemNotification({
    title,
    body: message,
    urgency: 'normal'
  });
};

/**
 * Show info notification
 */
export const showInfoNotification = (message: string, title: string = 'Information') => {
  return showSystemNotification({
    title,
    body: message,
    urgency: 'low'
  });
};

/**
 * Show delete confirmation dialog
 */
export const showDeleteConfirmation = (itemName: string) => {
  return showConfirmationDialog(
    `Are you sure you want to delete "${itemName}"?`,
    'Confirm Delete'
  );
};

/**
 * Show save confirmation dialog
 */
export const showSaveConfirmation = (message: string = 'Do you want to save your changes?') => {
  return showConfirmationDialog(message, 'Save Changes');
};

/**
 * Show exit confirmation dialog
 */
export const showExitConfirmation = (message: string = 'Are you sure you want to exit?') => {
  return showConfirmationDialog(message, 'Exit Application');
};
