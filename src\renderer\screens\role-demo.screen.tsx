import React from 'react';
import { useRoleAccess } from '../hooks/useRoleAccess';
import { RoleBasedComponent } from '../components/RoleBasedComponent';
import { RoleAccessSummary, RoleAccessBadge } from '../components/RoleAccessSummary';
import { Button } from '../components/button';

/**
 * Demo screen to showcase role-based access control functionality
 */
export function RoleDemoScreen() {
  const { userRole, hasWriteAccess, isAdmin, canCreate, canUpdate, canDelete, canExport } = useRoleAccess();

  const handleAction = (action: string) => {
    alert(`${action} action triggered!`);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Role-Based Access Control Demo</h1>
              <p className="text-gray-600 mt-1">
                Demonstration of role-based permissions in Master Data sections
              </p>
            </div>
            <RoleAccessBadge />
          </div>
        </div>

        {/* Role Access Summary */}
        <RoleAccessSummary />

        {/* Action Buttons Demo */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Action Buttons (Role-Based)</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            
            {/* Create Button */}
            <div className="space-y-2">
              <h3 className="font-medium text-gray-700">Create Action</h3>
              <RoleBasedComponent 
                requiredPermission="canCreate"
                fallback={
                  <Button variant="secondary" disabled className="w-full">
                    Create (Disabled)
                  </Button>
                }
              >
                <Button 
                  variant="primary" 
                  className="w-full"
                  onClick={() => handleAction('Create')}
                >
                  ➕ Create Record
                </Button>
              </RoleBasedComponent>
              <p className="text-xs text-gray-500">
                Available for: Admin, Maker
              </p>
            </div>

            {/* Update Button */}
            <div className="space-y-2">
              <h3 className="font-medium text-gray-700">Update Action</h3>
              <RoleBasedComponent 
                requiredPermission="canUpdate"
                fallback={
                  <Button variant="secondary" disabled className="w-full">
                    Update (Disabled)
                  </Button>
                }
              >
                <Button 
                  variant="primary" 
                  className="w-full"
                  onClick={() => handleAction('Update')}
                >
                  ✏️ Update Record
                </Button>
              </RoleBasedComponent>
              <p className="text-xs text-gray-500">
                Available for: Admin, Maker
              </p>
            </div>

            {/* Delete Button */}
            <div className="space-y-2">
              <h3 className="font-medium text-gray-700">Delete Action</h3>
              <RoleBasedComponent 
                requiredPermission="canDelete"
                fallback={
                  <Button variant="secondary" disabled className="w-full">
                    Delete (Disabled)
                  </Button>
                }
              >
                <Button 
                  variant="danger" 
                  className="w-full"
                  onClick={() => handleAction('Delete')}
                >
                  🗑️ Delete Record
                </Button>
              </RoleBasedComponent>
              <p className="text-xs text-gray-500">
                Available for: Admin, Maker
              </p>
            </div>

            {/* Export Button */}
            <div className="space-y-2">
              <h3 className="font-medium text-gray-700">Export Action</h3>
              <RoleBasedComponent 
                requiredPermission="canExport"
                fallback={
                  <Button variant="secondary" disabled className="w-full">
                    Export (Disabled)
                  </Button>
                }
              >
                <Button 
                  variant="secondary" 
                  className="w-full"
                  onClick={() => handleAction('Export')}
                >
                  📤 Export Data
                </Button>
              </RoleBasedComponent>
              <p className="text-xs text-gray-500">
                Available for: All roles
              </p>
            </div>
          </div>
        </div>

        {/* Admin-Only Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Admin-Only Features</h2>
          <RoleBasedComponent 
            requiredPermission="isAdmin"
            fallback={
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      Access Denied
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>This section is only available to administrators.</p>
                    </div>
                  </div>
                </div>
              </div>
            }
          >
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">
                    Admin Access Granted
                  </h3>
                  <div className="mt-2 text-sm text-green-700">
                    <p>You have administrator privileges and can access all features.</p>
                  </div>
                  <div className="mt-3">
                    <div className="flex gap-2">
                      <Button 
                        variant="primary" 
                        size="sm"
                        onClick={() => handleAction('User Management')}
                      >
                        👥 User Management
                      </Button>
                      <Button 
                        variant="primary" 
                        size="sm"
                        onClick={() => handleAction('Company Settings')}
                      >
                        🏢 Company Settings
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </RoleBasedComponent>
        </div>

        {/* Implementation Notes */}
        <div className="bg-blue-50 rounded-xl border border-blue-200 p-6">
          <h2 className="text-xl font-semibold text-blue-900 mb-4">Implementation Summary</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-blue-800 mb-2">✅ Implemented Features</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Role-based access control hooks</li>
                <li>• Master Data section permissions</li>
                <li>• Button-level access control</li>
                <li>• Export functionality restrictions</li>
                <li>• Admin-only features (Company Settings, User Management)</li>
                <li>• Visual feedback for restricted actions</li>
                <li>• Role information display</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-blue-800 mb-2">🎯 Role Permissions</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• <strong>Admin:</strong> Full access to all features</li>
                <li>• <strong>Maker:</strong> Create, update, delete master data</li>
                <li>• <strong>View:</strong> Read-only access with export capability</li>
                <li>• All roles can view, search, filter data</li>
                <li>• Company Settings: Admin only</li>
                <li>• User Management: Admin only</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
